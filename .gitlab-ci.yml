stages:
    - build
    - deploy-stage

build ui:
    environment: stage
    only:
        - master
    image: node:14-alpine
    stage: build
    script:
        - npm i
        - npm run build:stage
    artifacts:
        paths:
            - "dist"
        untracked: false
        expire_in: 20 min

deploy stage ui:
    environment: stage
    only:
        - master
    stage: deploy-stage
    image:
        name: amazon/aws-cli:2.4.23
        entrypoint:  [""]
    script:
        - aws s3 sync dist s3://$AWS_S3_BUCKET_CHURCH --delete
        - aws cloudfront create-invalidation --distribution-id $AWS_CLOUDFRONT_ID_CHURCH_ADMIN --paths '/*'
