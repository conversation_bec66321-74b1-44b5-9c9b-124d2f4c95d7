{"cSpell.words": ["cvalue", "darktoggle", "datatable", "horizontaltoggle", "IAPI", "minisidebar", "titlecase", "Topbar"], "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "editor.formatOnSave": true}, "[typescript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint", "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "editor.formatOnSave": true}}