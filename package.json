{"name": "church-app-admin", "version": "1.0.0", "scripts": {"ng": "ng", "prepare": "husky install", "start": "ng serve", "build:stage": "NODE_ENV=stage ng build --configuration stage", "build:prod": "NODE_ENV=production ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "eslint './src/**/*.ts'", "lint-fix": "npm run lint -- --fix"}, "private": true, "dependencies": {"@angular/animations": "~13.2.3", "@angular/cdk": "^13.0.1", "@angular/common": "~13.2.3", "@angular/compiler": "~13.2.3", "@angular/core": "~13.2.3", "@angular/flex-layout": "^13.0.0-beta.38", "@angular/forms": "~13.2.3", "@angular/material": "^13.2.3", "@angular/material-moment-adapter": "^13.0.1", "@angular/platform-browser": "~13.2.3", "@angular/platform-browser-dynamic": "~13.2.3", "@angular/router": "~13.2.3", "@auth0/angular-jwt": "^5.2.0", "@kolkov/angular-editor": "^3.0.0-beta.0", "@ngrx/store": "^13.0.2", "@swimlane/ngx-datatable": "^20.0.0", "angular-calendar": "^0.29.0", "angular-feather": "^6.3.0", "apexcharts": "^3.16.0", "ckeditor4-angular": "^3.3.0", "crypto-js": "^4.1.1", "date-fns": "^2.28.0", "moment": "^2.24.0", "ng-apexcharts": "1.5.6", "ng2-search-filter": "^0.5.1", "ngx-material-timepicker": "^5.5.3", "ngx-pagination": "^5.1.1", "ngx-perfect-scrollbar": "^10.1.1", "ngx-permissions": "^13.0.1", "rxjs": "~7.4.0", "tslib": "^2.3.0", "uuid": "^8.3.2", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~13.2.4", "@angular-eslint/builder": "13.2.1", "@angular-eslint/eslint-plugin": "13.2.1", "@angular-eslint/eslint-plugin-template": "13.2.1", "@angular-eslint/schematics": "13.2.1", "@angular-eslint/template-parser": "13.2.1", "@angular/cli": "~13.2.4", "@angular/compiler-cli": "~13.2.3", "@types/crypto-js": "^4.1.1", "@types/jasmine": "~3.10.0", "@types/node": "^12.11.1", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "5.17.0", "@typescript-eslint/parser": "5.17.0", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-simple-import-sort": "^7.0.0", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^7.0.4", "jasmine-core": "~3.10.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "prettier": "^2.6.2", "prettier-eslint": "^13.0.0", "typescript": "~4.4.3"}}