{"name": "church-app-admin", "version": "1.0.0", "scripts": {"ng": "ng", "prepare": "husky install", "start": "ng serve", "build": "ng build", "build:stage": "NODE_ENV=stage ng build --configuration stage", "build:prod": "NODE_ENV=production ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "eslint './src/**/*.ts'", "lint-fix": "npm run lint -- --fix"}, "private": true, "dependencies": {"@angular/animations": "^19.2.15", "@angular/cdk": "^19.2.19", "@angular/common": "^19.2.15", "@angular/compiler": "^19.2.15", "@angular/core": "^19.2.15", "@angular/forms": "^19.2.15", "@angular/material": "^19.2.19", "@angular/material-moment-adapter": "^19.2.19", "@angular/platform-browser": "^19.2.15", "@angular/platform-browser-dynamic": "^19.2.15", "@angular/router": "^19.2.15", "@auth0/angular-jwt": "^5.0.2", "@ckeditor/ckeditor5-angular": "^10.0.0", "@ckeditor/ckeditor5-build-classic": "^44.3.0", "@kolkov/angular-editor": "^3.0.0-beta.0", "@ngrx/effects": "^19.2.1", "@ngrx/entity": "^19.2.1", "@ngrx/store": "^19.2.1", "@ngrx/store-devtools": "^19.2.1", "@siemens/ngx-datatable": "^24.3.3", "angular-calendar": "^0.29.0", "angular-feather": "^6.5.1", "apexcharts": "^3.42.0", "crypto-js": "^4.1.1", "date-fns": "^2.28.0", "luxon": "^3.7.2", "moment": "^2.24.0", "ng-apexcharts": "^1.9.0", "ngx-material-timepicker": "^13.1.1", "ngx-pagination": "^5.1.1", "ngx-permissions": "^13.0.1", "ngx-scrollbar": "^18.0.0", "ngx-search-filter": "^18.0.0", "rxjs": "~7.4.0", "tslib": "^2.3.0", "uuid": "^8.3.2", "zone.js": "~0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.19", "@angular-eslint/builder": "^18.4.3", "@angular-eslint/eslint-plugin": "^19.8.1", "@angular-eslint/eslint-plugin-template": "^19.8.1", "@angular-eslint/template-parser": "^19.8.1", "@angular/cli": "^19.2.19", "@angular/compiler-cli": "^19.2.15", "@types/crypto-js": "^4.1.1", "@types/jasmine": "~3.10.0", "@types/node": "^12.11.1", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^8.46.3", "@typescript-eslint/parser": "^8.46.3", "eslint": "^9.39.1", "eslint-config-prettier": "^8.10.2", "eslint-plugin-prettier": "^4.2.5", "eslint-plugin-simple-import-sort": "^7.0.0", "eslint-plugin-unused-imports": "^4.3.0", "husky": "^7.0.4", "jasmine-core": "~3.10.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "prettier": "^2.8.8", "prettier-eslint": "^13.0.0", "typescript": "^5.8.2"}}