import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ActivatedRoute, Router } from '@angular/router';

import { MemberService } from '../services/member.service';
import { IInfo } from '../types/interfaces/church.interface';
import { Utils } from '../utils';
import Validation from '../validators/confirmed.validator';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss'],
})
export class ResetPasswordComponent implements OnInit {
  token!: string;
  msg = '';
  info!: IInfo;
  passwordHide = true;
  confirmPasswordHide = true;

  isToken: boolean = true;
  isSuccess: boolean = false;
  submitted: boolean = false;
  isLoading: boolean = false;

  resetPasswordForm: FormGroup = new FormGroup({});

  constructor(
    public snackBar: MatSnackBar,
    private route: ActivatedRoute,
    private router: Router,
    private service: MemberService,
    private fb: FormBuilder,
  ) {}

  ngOnInit(): void {
    this.isLoading = true;
    this.info = Utils.getInfo();

    this.route.params.subscribe((params) => {
      this.token = params['token'] != undefined ? params['token'] : 0;
      this.service.tokenVerification(this.token).subscribe((res) => {
        if (!res.status) {
          this.msg = res.message;
          this.isToken = false;
          this.isLoading = false;
          return;
        }
        this.isLoading = false;
        this.isToken = true;
      });
    });

    this.resetPasswordForm = this.fb.group(
      {
        password: ['', [Validators.required, Validators.minLength(6)]],
        confirmPassword: ['', Validators.required],
      },
      {
        validators: [Validation.match('password', 'confirmPassword')],
      },
    );
  }

  get f(): { [key: string]: AbstractControl } {
    return this.resetPasswordForm.controls;
  }

  onSubmit() {
    try {
      this.submitted = true;

      if (this.resetPasswordForm.invalid) {
        return;
      }

      const dataPass = {
        password: this.resetPasswordForm.value.password,
        token: this.token,
      };

      this.isLoading = true;
      this.service.resetPassword(dataPass).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.openSnackBar(res.message, 'Try again');
            return;
          }

          this.isLoading = false;
          this.isToken = false;
          this.isSuccess = true;
          this.msg = res.message;
        },
        error: (err) => {
          this.isLoading = false;
          this.msg = err.msg;
          return 'error';
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  openSnackBar(message: string, action: string) {
    this.snackBar.open(message, action, {
      duration: 1000,
    });
  }
}
