import {
  Directive,
  ElementRef,
  HostBinding,
  Input,
  NgModule,
  OnChanges,
  SecurityContext,
  SimpleChanges,
} from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';

@Directive({
  selector: '[appMapUrlFromText]',
})
export class MapUrlFromTextDirective implements OnChanges {
  @Input('appMapUrlFromText') urlParams: string = '';

  @Input() caseSensitive = false;

  @HostBinding('innerHtml')
  content!: string;
  constructor(private el: ElementRef, private sanitizer: DomSanitizer) {}

  ngOnChanges(changes: SimpleChanges) {
    if (this.urlParams) {
      const text = this.urlParams;

      let regex = new RegExp(/\b((https?|ftp|file):\/\/|(www.))[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|]/gi);

      let newText = text.replace(regex, (match: string) => {
        match =
          match.charAt(0).toLocaleLowerCase() === 'w'
            ? `http://${match.toLocaleLowerCase()}`
            : match.toLocaleLowerCase();
        return `<a href=${match} target="_blank"  class="url-highlight">${match}</a>`;
      });

      const sanitized = this.sanitizer.sanitize(SecurityContext.HTML, newText) || '';
      this.content = sanitized;
    }
  }
}

@NgModule({
  declarations: [MapUrlFromTextDirective],
  exports: [MapUrlFromTextDirective],
})
export class MapUrlFromTextDirectiveModule {}
