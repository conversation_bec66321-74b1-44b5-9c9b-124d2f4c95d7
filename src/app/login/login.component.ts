import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/services/auth.service';
import { CustomizerService } from 'src/app/services/customizer.service';

import { Utils } from '../utils';
import { IInfo } from './../types/interfaces/church.interface';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  providers: [AuthService],
})
export class LoginComponent implements OnInit {
  passwordHide = true;
  msg = '';
  loading: boolean = false;
  loginForm: FormGroup = new FormGroup({});
  submitted = false;
  info!: IInfo;

  constructor(
    private router: Router,
    public customizer: CustomizerService,
    private service: AuthService,
    private fb: FormBuilder,
  ) {}

  ngOnInit(): void {
    this.loginForm = this.fb.group({
      userName: ['', Validators.required],
      password: ['', [Validators.required, Validators.minLength(6)]],
    });

    this.info = Utils.getInfo();
  }

  get f(): { [key: string]: AbstractControl } {
    return this.loginForm.controls;
  }

  check() {
    try {
      this.submitted = true;

      if (this.loginForm.invalid) {
        return;
      }
      this.loading = true;
      const password = this.loginForm.value.password;
      const username = this.loginForm.value.userName;
      this.service.signIn({ username, password }).subscribe({
        next: (res) => {
          this.loading = false;
          if (res.status) {
            this.router.navigate(['/dashboard']);
            return;
          }

          this.submitted = true;
          this.msg = res.message;
        },
        error: (err) => {
          this.loading = false;
          this.msg = err.msg;
          return 'error';
        },
      });
    } catch (err) {
      this.loading = false;
      console.error(err);
    }
  }
}
