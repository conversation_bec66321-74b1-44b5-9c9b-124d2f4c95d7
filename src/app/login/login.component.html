<div *ngIf="loading" fxLayout="row " fxLayoutAlign="center center">
  <app-spinner></app-spinner>
</div>
<div fxLayout="row wrap" class="auth-wrapper">
  <div fxFlex="100" fxFlex.gt-md="60%" class="bg-white bg-church-landing-page">
    <div fxFlex="100" class="d-flex align-items-center justify-content-center detail-part">
      <div class="text-center bg-church-landing-page-logo">
        <img src="{{ info.logo }}" appLogoOnError />
        <h2 class="m-t-10 fw-bold align-items-center justify-content-center">{{ info.content | uppercase }}</h2>
      </div>
    </div>
  </div>
  <div fxFlex="100" fxFlex.gt-md="40%" class="d-flex align-items-center">
    <div fxLayout="row wrap" class="right-bg-content">
      <div fxFlex="100" fxFlex.sm="70%" fxFlex.gt-md="70%" fxFlex.gt-lg="50%">
        <div class="p-30">
          <!-- <div class="auth-logo-main">
            <img
              src="https://tithing-apps-church-resources-prod.s3.amazonaws.com/629269b6ec901a6259e65460/logo/629269b6ec901a6259e65460.png"
              alt="logo" />
          </div> -->
          <h2 class="fw-bold m-b-5 text-center text-primary">Sign In</h2>

          <form [formGroup]="loginForm" class="m-t-30">
            <div *ngIf="msg" class="bg-danger p-10 text-white">{{ msg }}</div>
            <mat-form-field appearance="outline">
              <mat-label>Username</mat-label>
              <input
                matInput
                name="userName"
                formControlName="userName"
                [ngClass]="{ 'is-invalid': submitted && f['userName'].errors }" />
            </mat-form-field>
            <div *ngIf="submitted && f['userName'].errors" class="invalid-feedback" class="">
              <div *ngIf="f['userName'].errors['required']">Username is required</div>
            </div>
            <mat-form-field appearance="outline">
              <mat-label>Password</mat-label>
              <input
                matInput
                type="password"
                minlength="6"
                name="password"
                formControlName="password"
                [type]="passwordHide ? 'password' : 'text'" />
              <mat-icon class="text-primary mat-icon-no-color" matSuffix (click)="passwordHide = !passwordHide">{{
                passwordHide ? 'visibility' : 'visibility_off'
              }}</mat-icon>
            </mat-form-field>
            <div *ngIf="submitted && f['password'].errors" class="invalid-feedback">
              <div *ngIf="f['password'].errors['required']">Password is required</div>
              <div *ngIf="f['password'].errors['minlength']">Password must be at least 6 characters</div>
            </div>
            <div class="hstack hstack-md m-t-10 m-b-15">
              <a routerLink="/forgot-password" class="text-accent mat-subheading-2 fw-medium">Forgot Password ?</a>
            </div>
            <button mat-flat-button color="primary" class="w-100" (click)="check()" [class.spinner]="loading">
              Sign In
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
