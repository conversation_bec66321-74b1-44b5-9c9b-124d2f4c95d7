<div *ngIf="loading" class="loading-container">
  <app-spinner></app-spinner>
</div>

<div class="auth-wrapper">
  <!-- Left Section -->
  <div class="auth-left bg-white bg-church-landing-page">
    <div class="detail-part">
      <div class="text-center bg-church-landing-page-logo">
        <img src="{{ info.logo }}" appLogoOnError />
        <h2 class="m-t-10 fw-bold">{{ info.content | uppercase }}</h2>
      </div>
    </div>
  </div>

  <!-- Right Section -->
  <div class="auth-right">
    <div class="right-bg-content">
      <div class="auth-form-container">
        <div class="p-30">
          <h2 class="fw-bold m-b-5 text-center text-primary">Sign In</h2>

          <form [formGroup]="loginForm" class="m-t-30">
            <div *ngIf="msg" class="bg-danger p-10 text-white">{{ msg }}</div>

            <mat-form-field appearance="outline">
              <mat-label>Username</mat-label>
              <input
                matInput
                name="userName"
                formControlName="userName"
                [ngClass]="{ 'is-invalid': submitted && f['userName'].errors }" />
            </mat-form-field>
            <div *ngIf="submitted && f['userName'].errors" class="invalid-feedback">
              <div *ngIf="f['userName'].errors['required']">Username is required</div>
            </div>

            <mat-form-field appearance="outline">
              <mat-label>Password</mat-label>
              <input
                matInput
                minlength="6"
                name="password"
                formControlName="password"
                [type]="passwordHide ? 'password' : 'text'" />
              <mat-icon class="text-primary mat-icon-no-color" matSuffix (click)="passwordHide = !passwordHide">
                {{ passwordHide ? 'visibility' : 'visibility_off' }}
              </mat-icon>
            </mat-form-field>
            <div *ngIf="submitted && f['password'].errors" class="invalid-feedback">
              <div *ngIf="f['password'].errors['required']">Password is required</div>
              <div *ngIf="f['password'].errors['minlength']">Password must be at least 6 characters</div>
            </div>

            <div class="forgot-link">
              <a routerLink="/forgot-password" class="text-accent mat-subheading-2 fw-medium"> Forgot Password? </a>
            </div>

            <button mat-flat-button color="primary" class="w-100" (click)="check()" [class.spinner]="loading">
              Sign In
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
