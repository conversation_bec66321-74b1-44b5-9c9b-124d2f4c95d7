<div class="auth-wrapper">
  <!-- Left Section -->
  <div class="auth-left">
    <div class="detail-part">
      <div class="text-center bg-church-landing-page-logo">
        <img src="{{ info.logo }}" appLogoOnError />
        <h2 class="m-t-10 fw-bold">{{ info.content | uppercase }}</h2>
      </div>
    </div>
  </div>

  <!-- Right Section -->
  <div class="auth-right">
    <div class="right-bg-content">
      <div class="auth-form-container">
        <div class="p-30">
          <h2 class="fw-bold m-b-5 text-primary">Forgot your password?</h2>
          <div class="text-muted mat-subheading-2 fw-normal lh-md m-b-15">
            Please enter the email address associated with your account, and we’ll email you a link to reset your
            password.
          </div>

          <form class="m-t-30">
            <mat-form-field appearance="outline">
              <mat-label>Email Address</mat-label>
              <input matInput placeholder="Enter your email" [formControl]="email" required />
              <mat-error *ngIf="email.invalid">{{ getErrorMessage() }}</mat-error>
            </mat-form-field>

            <button
              mat-flat-button
              color="primary"
              class="w-100"
              [class.spinner]="loading"
              [disabled]="!email.valid || loading"
              (click)="submit()">
              Submit
            </button>

            <a routerLink="/login">
              <button mat-button color="primary" class="w-100 m-t-15">Back to Login</button>
            </a>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
