.mat-card {
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}
/* === Layout Wrapper === */
.auth-wrapper {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  min-height: 100vh;
}

/* === Left Panel === */
.auth-left {
  flex: 1 1 100%;
  background-color: #fff;
  background-size: cover;
  background-position: center;
}

@media (min-width: 960px) {
  .auth-left {
    flex: 0 0 60%;
  }
}

/* Inner content alignment */
.detail-part {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

/* === Right Panel === */
.auth-right {
  flex: 1 1 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--mat-surface-container-lowest);
}

@media (min-width: 960px) {
  .auth-right {
    flex: 0 0 40%;
  }
}

/* Right content container */
.right-bg-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  width: 100%;
}

/* Inner form container */
.auth-form-container {
  flex: 1 1 100%;
  max-width: 500px;
}

@media (min-width: 600px) {
  .auth-form-container {
    flex: 0 0 70%;
  }
}

@media (min-width: 1200px) {
  .auth-form-container {
    flex: 0 0 50%;
  }
}

/* === Utilities === */
.text-center {
  text-align: center;
}

.p-30 {
  padding: 30px;
}

.m-t-10 {
  margin-top: 10px;
}

.m-t-15 {
  margin-top: 15px;
}

.m-b-5 {
  margin-bottom: 5px;
}

.m-b-15 {
  margin-bottom: 15px;
}

.w-100 {
  width: 100%;
}

.fw-bold {
  font-weight: bold;
}

.text-muted {
  color: #757575;
}

/* Spinner effect */
.spinner {
  opacity: 0.7;
  pointer-events: none;
}
