<mat-card class="m-b-0">
  <mat-card-content>
    <div class="d-flex align-items-center p-b-10">
      <mat-card-title>Logged In Users</mat-card-title>
      <section class="ml-auto">
        <mat-radio-group (change)="chartFilter($event)" [ngModel]="chartDataModel">
          <mat-radio-button value="week">One Week</mat-radio-button>
          <mat-radio-button value="month">One Month</mat-radio-button>
        </mat-radio-group>
      </section>
    </div>
    <div class="position-relative">
      <mat-divider></mat-divider>
    </div>

    <div id="chart" class="d-flex flex-column m-t-30 position-relative">
      <apx-chart
        [series]="barChartOptions.series"
        [chart]="barChartOptions.chart"
        [dataLabels]="barChartOptions.dataLabels"
        [plotOptions]="barChartOptions.plotOptions"
        [xaxis]="barChartOptions.xaxis"></apx-chart>
    </div>

    <div class="p-b-20"></div>
    <br />
  </mat-card-content>
</mat-card>
