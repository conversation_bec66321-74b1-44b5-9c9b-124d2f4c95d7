.logo-img {
  height: 100px;
  width: 100px;
  object-fit: contain;
}

.spinner::before {
  width: 50px;
  height: 50px;
  top: 30%;
  border: 2px solid #c9c5c5;
  border-top-color: #fb9778;
}
.welcome-card {
  position: relative;
}
/* Flex utilities */
.d-flex {
  display: flex;
}

.flex-wrap {
  flex-wrap: wrap;
}

.align-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

/* Flex basis and responsive control */
.flex-100 {
  flex: 1 1 100%;
}

@media (min-width: 600px) {
  /* replaces fxFlex.gt-xs */
  .flex-xs-50 {
    flex: 1 1 50%;
  }
}

/* Additional styling */
.lh-lg {
  line-height: 1.6;
}

.logo-img {
  max-width: 100%;
  height: auto;
  display: block;
}
