import { Component, OnInit } from '@angular/core';
import { DashboardService } from 'src/app/services/dashboard.service';
import { IChurchCollection } from 'src/app/types/interfaces/church.interface';

@Component({
  selector: 'app-welcome-card',
  templateUrl: './welcome-card.component.html',
  styleUrls: ['./welcome-card.component.scss'],
})
export class WelcomeCardComponent implements OnInit {
  adminDashboard!: IChurchCollection;
  isLoading: boolean = false;
  msg = '';

  constructor(private dashboardService: DashboardService) {}

  ngOnInit(): void {
    this.getChurchAdminDetails();
  }

  getChurchAdminDetails() {
    try {
      this.isLoading = true;
      this.dashboardService.getChurchAdminDetails().subscribe({
        next: (res) => {
          if (!res.status || !res.data) {
            this.isLoading = false;
            this.msg = res.message;
            return;
          }
          this.isLoading = false;

          this.adminDashboard = res.data[0];
        },
        error: (err) => {
          this.isLoading = false;
          this.msg = err.error.message;
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }
}
