<mat-card class="m-b-0">
  <mat-card-content style="height: 552px">
    <div class="d-flex align-items-center p-b-10">
      <mat-card-title>Storage Percentage</mat-card-title>
    </div>
    <div class="position-relative">
      <mat-divider></mat-divider>
    </div>
    <div id="chart" class="d-flex flex-column m-t-30 position-relative">
      <apx-chart
        [series]="totalStoragePercentage.series"
        [chart]="totalStoragePercentage.chart"
        [legend]="totalStoragePercentage.legend"
        [labels]="totalStoragePercentage.labels"
        [dataLabels]="totalStoragePercentage.dataLabels"
        [colors]="totalStoragePercentage.colors"
        [stroke]="totalStoragePercentage.stroke"
        [tooltip]="totalStoragePercentage.tooltip"
        [plotOptions]="totalStoragePercentage.plotOptions"></apx-chart>
      <!-- <span class="center-icon">
        <i-feather name="disc" class="text-muted feather-30"></i-feather>
      </span> -->
    </div>
    <div class="hstack align-items-center justify-content-between m-t-15">
      <h6 class="mat-body-2 m-b-0 text-muted">
        <span class="m-r-5"><i-feather name="circle" class="feather-10 fill-accent"></i-feather></span>Video
      </h6>
      <h6 class="mat-body-2 m-b-0 text-muted">
        <span class="m-r-5"><i-feather name="circle" class="feather-10 fill-primary"></i-feather></span>Audio
      </h6>
      <h6 class="mat-body-2 m-b-0 text-muted">
        <span class="m-r-5"><i-feather name="circle" class="feather-10 fill-warning"></i-feather></span>Free
      </h6>
    </div>
  </mat-card-content>
</mat-card>
