import { Component, Inject, OnInit, Optional } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { IMediaCollection } from 'src/app/types/interfaces/media.interface';

@Component({
  selector: 'app-media-dialog',
  templateUrl: './media-dialog.component.html',
  styleUrls: ['./media-dialog.component.scss'],
})
export class MediaDialogComponent implements OnInit {
  localData: IMediaCollection;

  constructor(
    public dialogRef: MatDialogRef<MediaDialogComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: IMediaCollection,
  ) {
    this.localData = { ...data };
    dialogRef.disableClose = true;
  }

  ngOnInit(): void {}

  closeDialog(): void {
    this.dialogRef.close({ event: 'Cancel' });
  }
}
