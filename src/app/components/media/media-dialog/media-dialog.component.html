<div *ngIf="localData.type === 'video'" mat-dialog-content>
  <div class="m-b-10" fxLayoutAlign="space-between center" fxLayoutAlign.lt-sm="space-between center">
    <div>{{ localData.title }}</div>
    <i-feather name="x" class="text-danger feather-18" (click)="closeDialog()"></i-feather>
  </div>
  <video *ngIf="!localData.isLoading; else loader" width="320" height="240" controls>
    <source src="{{ localData.playLink }}" type="video/mp4" />
    Sorry, your browser doesn't support this videos.
  </video>
  <ng-template #loader>
    <div>Loading!!!</div>
  </ng-template>
</div>

<div *ngIf="localData.type === 'audio'" mat-dialog-content>
  <div class="m-b-10" fxLayoutAlign="space-between center" fxLayoutAlign.lt-sm="space-between center">
    <div>{{ localData.title }}</div>
    <i-feather name="x" class="text-danger feather-18 cursor-pointer" (click)="closeDialog()"></i-feather>
  </div>
  <audio controls>
    <source src="{{ localData.media }}" type="audio/mpeg" />
    Your browser does not support the audio element.
  </audio>
</div>
