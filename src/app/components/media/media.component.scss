.media-thumbnail {
  position: relative;
  display: inline-block;
  cursor: pointer;
  text-align: center;

  &:before {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    content: '\e1c4';
    font-family: 'Material Icons';
    font-size: 80px;
    color: #fff;
    opacity: 0.8;
    text-shadow: 0px 0px 30px rgba(0, 0, 0, 0.5);
  }
  &:hover:before {
    color: #eee;
  }
}


.mat-ripple-style {
  height: 200px;
}

.media-card-footer{
  margin: 0px 20px 0px 0px !important;
}

.media-content{
  min-height: 60vh;
}
