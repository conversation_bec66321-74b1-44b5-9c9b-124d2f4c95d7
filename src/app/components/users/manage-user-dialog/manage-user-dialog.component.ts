import { Component, Inject, OnInit, Optional } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AdminService } from 'src/app/services/admin.service';
import { IAdminCollection } from 'src/app/types/interfaces/admin.interface';
import { IGeneratePassword } from 'src/app/types/interfaces/resetpassword.interface';
import { IUserCollectionWithAction } from 'src/app/types/interfaces/user.interface';

import { IUserCollection } from './../../../types/interfaces/user.interface';
import { passwordGenerate } from './../../../utils/generate-password';

enum Actions {
  ACTIVATE = 'Activate',
  DEACTIVATE = 'Deactivate',
  UPDATE = 'Update',
  DELETE = 'Delete',
  RESEND_EMAIL = 'Resend Email',
  ADD = 'Add',
  PASSWORD = 'Generate Password',
}

@Component({
  selector: 'app-dialog',
  templateUrl: './manage-user-dialog.component.html',
  styleUrls: ['./manage-user-dialog.component.scss'],
})
export class ManageUserDialogComponent implements OnInit {
  userData: IUserCollectionWithAction;
  passwordData: IGeneratePassword = {
    password: '',
    _id: '',
  };
  isLoading: boolean = false;
  isGeneratePassword: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<ManageUserDialogComponent>,
    private service: AdminService,
    private snackBar: MatSnackBar,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: IUserCollectionWithAction,
  ) {
    this.userData = data;
    dialogRef.disableClose = true;

    if (this.userData.action === 'Generate Password') {
      this.generatePassword();
      this.isGeneratePassword = true;
      this.passwordData._id = data._id;
    }
  }

  ngOnInit(): void {}

  doAction(): void {
    const expression = this.userData.action;
    switch (expression) {
      case Actions.RESEND_EMAIL:
        this.resendEmail(this.userData);
        break;
      case Actions.ACTIVATE:
      case Actions.DEACTIVATE:
        this.activateOrDeactivateUser(this.userData);
        break;
      case Actions.ADD:
        this.addUser(this.userData);
        break;
      case Actions.UPDATE:
        this.updateUser(this.userData);
        break;
      case Actions.DELETE:
        this.deleteUser(this.userData);
        break;
      case Actions.PASSWORD:
        this.updateGeneratedPassword(this.passwordData);
        break;
    }
  }

  closeDialog(): void {
    this.dialogRef.close({ event: 'Cancel' });
  }

  activateOrDeactivateUser(data: IUserCollectionWithAction) {
    try {
      data.isActive = !data.isActive;
      this.isLoading = true;
      this.service.activateOrDeactivateUser(data._id, data.isActive).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return;
          }

          this.isLoading = false;
          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  addUser(data: IAdminCollection) {
    try {
      this.isLoading = true;
      this.service.addUser(data).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return;
          }

          this.isLoading = false;
          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  updateUser(data: IUserCollection) {
    try {
      this.isLoading = true;
      this.service.updateUser(data).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return;
          }

          this.isLoading = false;
          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  deleteUser(data: IUserCollection) {
    try {
      this.isLoading = true;
      this.service.deleteUser(data._id).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return;
          }

          this.isLoading = false;
          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  resendEmail(data: IAdminCollection) {
    try {
      this.isLoading = true;
      this.service.resendEmail(data._id).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return;
          }

          this.isLoading = false;
          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  async generatePassword() {
    try {
      this.passwordData.password = passwordGenerate();
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  updateGeneratedPassword(data: IGeneratePassword) {
    try {
      this.isLoading = true;
      this.service.updatePassword(data).subscribe({
        next: (res) => {
          if (!res.status) {
            this.isLoading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return;
          }

          this.isLoading = false;
          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }
}
