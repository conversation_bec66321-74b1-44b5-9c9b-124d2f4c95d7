import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import { AdminService } from 'src/app/services/admin.service';
import { IUserCollection } from 'src/app/types/interfaces/user.interface';

import { ManageUserDialogComponent } from '../manage-user-dialog/manage-user-dialog.component';
import { IUserCollectionWithAction } from './../../../types/interfaces/user.interface';

@Component({
  selector: 'app-manage-user',
  templateUrl: './manage-user.component.html',
  styleUrls: ['./manage-user.component.scss'],
})
export class ManageUserComponent implements OnInit, AfterViewInit {
  totalRows = 100;
  pageSize = 25;
  currentPage = 0;
  msg = '';
  searchText = '';
  isLoading: boolean = false;
  noMedia: boolean = false;

  @ViewChild(MatTable, { static: true }) table: MatTable<IUserCollection> = Object.create(null);
  displayedColumns: string[] = ['#', 'name', 'email', 'emailVerificationStatus', 'status', 'action'];
  dataSource = new MatTableDataSource();
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator = Object.create(null);

  constructor(public dialog: MatDialog, private service: AdminService) {}

  ngOnInit(): void {
    this.getUsers(this.currentPage, this.pageSize, this.searchText);
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
  }

  applyFilter(filterValue: string): void {
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  openDialog(action: string, obj: IUserCollectionWithAction): void {
    obj.action = action;
    const dialogRef = this.dialog.open(ManageUserDialogComponent, { data: obj });
    dialogRef.afterClosed().subscribe((result) => {
      this.getUsers(this.currentPage, this.pageSize, this.searchText);
    });
  }

  createUser(action: string, data: Object): void {
    let obj = { ...data, action };
    obj.action = action;
    const dialogRef = this.dialog.open(ManageUserDialogComponent, { data: obj });
    dialogRef.afterClosed().subscribe((result) => {
      this.getUsers(this.currentPage, this.pageSize, this.searchText);
    });
  }

  getUsers(page: number, pageSize: number, search: string) {
    try {
      this.isLoading = true;
      this.service.getAllUsers(page, pageSize, search).subscribe({
        next: (res) => {
          if (!res.status || !res.data) {
            this.isLoading = false;
            this.noMedia = true;
            this.msg = res.message;
            return;
          }

          this.noMedia = false;
          this.isLoading = false;
          this.dataSource.data = res.data.result;
          setTimeout(() => {
            this.paginator.pageIndex = this.currentPage;
            this.paginator.length = res.data?.total;
          });
        },
        error: (err) => {
          this.isLoading = false;
          this.msg = err.error.message;
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }

  pageChanged(event: PageEvent) {
    this.pageSize = event.pageSize;
    this.currentPage = event.pageIndex;
    this.getUsers(this.currentPage, this.pageSize, this.searchText);
  }

  searchUser() {
    this.getUsers(this.currentPage, this.pageSize, this.searchText);
    this.paginator.firstPage();
  }

  clearSearch() {
    this.searchText = '';
    this.getUsers(this.currentPage, this.pageSize, this.searchText);
  }
}
