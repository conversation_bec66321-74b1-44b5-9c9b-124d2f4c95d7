<div *ngIf="isLoading" fxLayout="row " fxLayoutAlign="center center">
  <app-spinner></app-spinner>
</div>
<div fxLayout="row wrap">
  <div fxFlex="100">
    <mat-card>
      <mat-card-content>
        <div fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="20px">
          <div fxFlex.gt-md="25" fxFlex.gt-lg="25" fxFlex="100">
            <mat-form-field>
              <input
                matInput
                placeholder="Search User"
                (keyup.enter)="searchUser()"
                [(ngModel)]="searchText"
                autocomplete="off" />
              <button
                *ngIf="searchText"
                mat-button
                matSuffix
                mat-icon-button
                aria-label="Clear"
                (click)="clearSearch()">
                <mat-icon>close</mat-icon>
              </button>
            </mat-form-field>
          </div>
          <div fxFlex.gt-md="10%" fxFlex.gt-lg="10%" fxFlex="100%">
            <button mat-flat-button (click)="createUser('Add', {})" color="accent">
              <i-feather name="plus-circle" class="feather-15"></i-feather>
              Add User
            </button>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
<h2 class="mat-h2 text-center m-t-10" *ngIf="noMedia">No users found</h2>
<div fxLayout="row wrap">
  <div fxFlex="100">
    <mat-card *ngIf="!noMedia">
      <mat-card-content>
        <mat-card-title class="m-l-10">Manage Users</mat-card-title>

        <div class="table-responsive">
          <table mat-table [dataSource]="dataSource" class="w-100">
            <ng-container matColumnDef="#">
              <th mat-header-cell *matHeaderCellDef>#</th>
              <td mat-cell *matCellDef="let element; let i = index">
                {{
                  dataSource.paginator?.pageIndex === 0
                    ? i + 1
                    : 1 + i + (dataSource.paginator?.pageIndex || 0) * (dataSource.paginator?.pageSize || 0)
                }}
              </td>
            </ng-container>

            <ng-container matColumnDef="name">
              <th mat-header-cell *matHeaderCellDef>Name</th>
              <td mat-cell *matCellDef="let element">
                <div class="d-flex align-items-center">
                  <div>
                    <p class="fw-medium mat-subheading-1 m-b-0 m-t-0 text-capitalize">
                      {{ element.name | titlecase }}
                    </p>
                  </div>
                </div>
              </td>
            </ng-container>

            <ng-container matColumnDef="email">
              <th mat-header-cell *matHeaderCellDef>Email</th>
              <td mat-cell *matCellDef="let element">
                {{ element.email }}
              </td>
            </ng-container>
            <ng-container matColumnDef="role">
              <th mat-header-cell *matHeaderCellDef>Role</th>
              <td mat-cell *matCellDef="let element">
                {{ element.role }}
              </td>
            </ng-container>
            <ng-container matColumnDef="emailVerificationStatus">
              <th mat-header-cell *matHeaderCellDef>Email Verification</th>
              <td mat-cell *matCellDef="let element">
                <i-feather
                  *ngIf="element.emailVerificationStatus; else isEmailVerificationStatus"
                  name="check"
                  class="text-success feather-18"></i-feather>
                <ng-template #isEmailVerificationStatus>
                  <i-feather name="x" class="text-danger feather-18"></i-feather>
                </ng-template>
              </td>
            </ng-container>

            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef>Status</th>
              <td mat-cell *matCellDef="let element">
                <i-feather
                  *ngIf="element.isActive; else isActive"
                  name="check"
                  class="text-success feather-18"></i-feather>
                <ng-template #isActive>
                  <i-feather name="x" class="text-danger feather-18"></i-feather>
                </ng-template>
              </td>
            </ng-container>

            <ng-container matColumnDef="action">
              <th mat-header-cell *matHeaderCellDef>Action</th>
              <td mat-cell *matCellDef="let element" class="action-link">
                <button
                  (click)="openDialog('Resend Email', element)"
                  mat-icon-button
                  color="accent"
                  [disabled]="element.emailVerificationStatus"
                  title="Resend Mail">
                  <i-feather name="send" class="feather-18"></i-feather>
                </button>
                <button mat-icon-button (click)="openDialog('Update', element)" class="m-r-10" title="Edit">
                  <i-feather name="edit" class="text-primary feather-18"></i-feather>
                </button>
                <button
                  mat-icon-button
                  *ngIf="element.isActive; else isActive"
                  (click)="openDialog('Deactivate', element)"
                  class="m-r-10"
                  title="Deactivate User">
                  <i-feather name="eye-off" class="text-danger feather-18"></i-feather>
                </button>
                <ng-template #isActive>
                  <button
                    mat-icon-button
                    (click)="openDialog('Activate', element)"
                    class="m-r-10"
                    title="Activate User">
                    <i-feather name="eye" class="text-success feather-18"></i-feather>
                  </button>
                </ng-template>
                <button mat-icon-button (click)="openDialog('Delete', element)" class="m-r-10" title="Delete">
                  <i-feather name="trash" class="text-danger feather-18"></i-feather>
                </button>
                <button
                  mat-icon-button
                  (click)="openDialog('Generate Password', element)"
                  class="m-r-10"
                  title="Generate Password">
                  <i-feather name="lock" class="text-success feather-18"></i-feather>
                </button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>

<mat-card>
  <div align="end">
    <mat-paginator
      #paginator
      [length]="totalRows"
      [pageIndex]="currentPage"
      [pageSize]="pageSize"
      (page)="pageChanged($event)"
      [pageSizeOptions]="[25, 50, 75, 100]"
      aria-label="Select page">
    </mat-paginator>
  </div>
</mat-card>
