<div *ngIf="loading" fxLayout="row " fxLayoutAlign="center center">
  <app-spinner></app-spinner>
</div>
<div fxLayout="row wrap">
  <div fxFlex="100%">
    <mat-card class="m-b-0">
      <mat-card-content> <mat-card-title>Church Settings</mat-card-title></mat-card-content>
    </mat-card>
  </div>
</div>

<div fxLayout="row wrap">
  <div fxFlex="100">
    <mat-card>
      <mat-card-content>
        <div fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="20px">
          <div></div>
          <div fxFlex.gt-md="25" fxFlex.gt-lg="25" fxFlex="100">
            <mat-checkbox [(ngModel)]="login" [ngModelOptions]="{ standalone: true }" (change)="checkValue($event)">
              Login Required</mat-checkbox
            >
          </div>
          <div fxFlex.gt-md="30%" fxFlex.gt-md="30%" fxLayoutAlign="">
            <button mat-flat-button (click)="update()" color="primary">Update</button>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
