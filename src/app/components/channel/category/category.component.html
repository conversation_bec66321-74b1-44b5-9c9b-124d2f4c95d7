<div *ngIf="isLoading" fxLayout="row " fxLayoutAlign="center center">
  <app-spinner></app-spinner>
</div>

<div fxLayout="row wrap">
  <div fxFlex="100">
    <mat-card>
      <mat-card-content>
        <div fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="20px">
          <div fxFlex.gt-md="25" fxFlex.gt-lg="25" fxFlex="100">
            <mat-form-field>
              <input
                matInput
                placeholder="Search Category"
                (keyup.enter)="searchCategory()"
                [(ngModel)]="searchText"
                autocomplete="off" />

              <button
                *ngIf="searchText"
                mat-button
                matSuffix
                mat-icon-button
                aria-label="Clear"
                (click)="clearSearch()">
                <mat-icon>close</mat-icon>
              </button>
            </mat-form-field>
          </div>
          <div fxFlex.gt-md="30%" fxFlex.gt-md="30%" fxLayoutAlign="end">
            <button mat-flat-button (click)="openDialog('Add Category', {})" color="accent">
              <i-feather name="plus-circle" class="feather-15"></i-feather>
              Add Category
            </button>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
<h2 class="mat-h2 text-center m-t-10" *ngIf="noMedia">{{ msg }} !</h2>
<div fxLayout="row wrap" class="category-content">
  <div fxFlex="100">
    <mat-card *ngIf="!noMedia">
      <mat-card-content>
        <div class="table-responsive">
          <table mat-table [dataSource]="dataSource" class="w-100">
            <ng-container matColumnDef="#">
              <th mat-header-cell *matHeaderCellDef>#</th>
              <td mat-cell *matCellDef="let element; let i = index">
                {{
                  dataSource.paginator?.pageIndex === 0
                    ? i + 1
                    : 1 + i + (dataSource.paginator?.pageIndex || 0) * (dataSource.paginator?.pageSize || 0)
                }}
              </td>
            </ng-container>

            <ng-container matColumnDef="name">
              <th mat-header-cell *matHeaderCellDef>Category Name</th>
              <td mat-cell *matCellDef="let element">
                <div class="d-flex align-items-center">
                  <img
                    class="thumbnail-img"
                    alt="logo"
                    [src]="element.thumbnail"
                    onerror="this.src='assets/images/backgrounds/thumbnail-dummy-image.svg'" />
                  <div class="m-l-15">
                    <p class="fw-medium mat-subheading-1 m-b-0 m-t-0 text-capitalize">
                      {{ element.name | titlecase }}
                    </p>
                  </div>
                </div>
              </td>
            </ng-container>

            <ng-container matColumnDef="type">
              <th mat-header-cell *matHeaderCellDef>Type</th>
              <td mat-cell *matCellDef="let element">
                {{ element.type | titlecase }}
              </td>
            </ng-container>

            <ng-container matColumnDef="updatedAt">
              <th mat-header-cell *matHeaderCellDef>Modified On</th>
              <td mat-cell *matCellDef="let element">
                {{ element.updatedAt | date }}
              </td>
            </ng-container>

            <ng-container matColumnDef="action">
              <th mat-header-cell *matHeaderCellDef>Actions</th>
              <td mat-cell *matCellDef="let element" class="action-link">
                <a (click)="openDialog('Update Category', element)" class="m-r-10 cursor-pointer" title="Edit">
                  <i-feather name="edit" class="text-primary feather-18"></i-feather>
                </a>
                <a (click)="openDialog('Delete Category', element)" class="m-r-10 cursor-pointer" title="Delete">
                  <i-feather name="trash" class="text-danger feather-18"></i-feather>
                </a>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>

<mat-card>
  <div align="end">
    <mat-paginator
      #paginator
      [length]="totalRows"
      [pageIndex]="currentPage"
      [pageSize]="pageSize"
      (page)="pageChanged($event)"
      [pageSizeOptions]="[25, 50, 75, 100]"
      aria-label="Select page">
    </mat-paginator>
  </div>
</mat-card>
