import { DatePipe } from '@angular/common';
import { Component, Inject, OnInit, Optional } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { CategoryService } from 'src/app/services/category.service';
import { IAPIResponse } from 'src/app/types/interfaces/api.interface';
import { ICategoryCollection, ICategoryUpload, ICategoryWithAction } from 'src/app/types/interfaces/category.interface';

enum Actions {
  ADD_CATEGORY = 'Add Category',
  UPDATE_CATEGORY = 'Update Category',
  DELETE_CATEGORY = 'Delete Category',
}

@Component({
  selector: 'app-dialog',
  templateUrl: './dialog.component.html',
  styleUrls: ['./dialog.component.scss'],
})
export class DialogComponent implements OnInit {
  action: string;
  localData: ICategoryWithAction; //two different obj action & data
  imageUpload!: File;
  loading: boolean = false;

  constructor(
    public datePipe: DatePipe,
    public dialogRef: MatDialogRef<DialogComponent>,
    private categoryService: CategoryService,
    private snackBar: MatSnackBar,

    @Optional() @Inject(MAT_DIALOG_DATA) public data: ICategoryWithAction,
  ) {
    this.localData = data;
    this.action = this.localData.action;
    dialogRef.disableClose = true;
  }

  ngOnInit(): void {}

  doAction(): void {
    const expression = this.localData.action;
    if (expression === Actions.ADD_CATEGORY) {
      if (!this.imageUpload) {
        this.snackBar.open('Please, upload thumbnail', 'ok', {
          duration: 5000,
        });
        return;
      }
    }

    const dataPass = {
      categoryData: this.localData,
      uploadImage: this.imageUpload,
    };

    switch (expression) {
      case Actions.ADD_CATEGORY:
        this.addCategory(dataPass);
        break;
      case Actions.UPDATE_CATEGORY:
        this.updateCategory(dataPass);
        break;
      case Actions.DELETE_CATEGORY:
        this.deleteCategory(this.localData);
        break;
    }
  }

  closeDialog(): void {
    this.dialogRef.close({ event: 'Cancel' });
  }

  selectImage(event: Event): void {
    const element = event.currentTarget as HTMLInputElement;
    let fileList: FileList | null = element.files;

    if (!fileList || fileList.length === 0) {
      return;
    }

    const mimeType = fileList[0].type;
    if (mimeType.match(/image\/*/) == null) {
      return;
    }

    const reader = new FileReader();
    reader.readAsDataURL(fileList[0]);
    reader.onload = (_event) => {
      this.localData.thumbnail = reader.result;
    };
    this.imageUpload = <File>fileList[0];
  }

  addCategory(rowObj: ICategoryUpload): void {
    try {
      this.loading = true;
      this.categoryService.createCategory(rowObj.categoryData, this.imageUpload).subscribe({
        next: (res: IAPIResponse) => {
          this.loading = false;

          if (!res.status) {
            this.loading = false;
            this.snackBar.open(res.message, 'ok', {
              duration: 6000,
            });
            return 'error';
          }

          this.snackBar.open(res.message, 'ok', {
            duration: 1000,
          });
          this.dialogRef.close();

          return 'success';
        },
        error: (err) => {
          this.loading = false;
          this.snackBar.open(err.error.message, 'ok', { duration: 6000 });
          return 'error';
        },
      });
    } catch (err) {
      this.loading = false;
      console.error(err);
    }
  }

  updateCategory(data: ICategoryUpload) {
    try {
      this.loading = true;
      this.categoryService.updateCategory(data.categoryData, this.imageUpload).subscribe({
        next: (res: IAPIResponse) => {
          this.loading = false;

          if (!res.status) {
            this.loading = false;
            this.snackBar.open(res.message, 'ok', { duration: 6000 });
            return 'error';
          }

          this.snackBar.open(res.message, 'ok', { duration: 1000 });
          this.dialogRef.close();

          return 'success';
        },
        error: (err) => {
          this.loading = false;
          this.snackBar.open(err.error.message, 'ok', { duration: 6000 });
          return 'error';
        },
      });
    } catch (err) {
      this.loading = false;
      console.error(err);
    }
  }

  deleteCategory(data: ICategoryCollection) {
    try {
      this.loading = true;
      this.categoryService.deleteCategory(data._id).subscribe({
        next: (res: IAPIResponse) => {
          if (!res.status) {
            this.loading = false;
            this.snackBar.open(res.message, 'ok', { duration: 6000 });
            return 'error';
          }

          this.loading = false;
          this.snackBar.open(res.message, 'ok', { duration: 1000 });
          this.dialogRef.close();
          return 'success';
        },
        error: (err) => {
          this.loading = false;
          this.snackBar.open(err.error.message, 'ok', { duration: 6000 });
          return 'error';
        },
      });
    } catch (err) {
      this.loading = false;
      console.error(err);
    }
  }

  clearUploads() {
    this.localData.thumbnail = '';
    this.imageUpload = undefined as unknown as File;
    console.log(this.imageUpload);
  }
}
