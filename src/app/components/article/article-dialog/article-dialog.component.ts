import { DatePipe } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, Inject, OnInit, Optional } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import * as ClassicEditorBuild from '@ckeditor/ckeditor5-build-classic';
import { ArticleService } from 'src/app/services/article.service';
import { IAPIResponse } from 'src/app/types/interfaces/api.interface';
import { IArticleCollection } from 'src/app/types/interfaces/article';

interface LocalData extends IArticleCollection {
  action: string;
  name: string;
}

enum Actions {
  ADD_ARTICLE = 'Add Article',
  UPDATE_ARTICLE = 'Update Article',
  DELETE_ARTICLE = 'Delete Article',
}

@Component({
  selector: 'app-article-dialog',
  templateUrl: './article-dialog.component.html',
  styleUrls: ['./article-dialog.component.scss'],
  standalone: false,
})
export class ArticleDialogComponent implements OnInit {
  localData!: LocalData; //two different obj action & data
  thumbnailUpload!: File;
  articleImgUpload!: File;
  action: string;
  loading: boolean = false;
  draftClick: boolean = false;
  draftLoading: boolean = false;
  publishClick: boolean = false;
  isClearUpload: boolean = false;
  articleImage?: string;
  thumbnail?: string;
  //ck Editor config
  // ckEditorConfig = {
  //   toolbar: [
  //     ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript', '-'],
  //     ['Undo', 'Redo'],
  //     [
  //       'NumberedList',
  //       'BulletedList',
  //       '-',
  //       'Outdent',
  //       'Indent',
  //       '-',
  //       'JustifyLeft',
  //       'JustifyCenter',
  //       'JustifyRight',
  //       'JustifyBlock',
  //       '-',
  //     ],
  //     ['Format', 'FontSize'],
  //     ['TextColor', 'BGColor'],
  //   ],
  // };
  public Editor: any = ClassicEditorBuild;

  public ckEditorConfig = {
    toolbar: [
      'undo',
      'redo',
      '|',
      'bold',
      'italic',
      'underline',
      'strikethrough',
      'subscript',
      'superscript',
      '|',
      'numberedList',
      'bulletedList',
      '|',
      'outdent',
      'indent',
      '|',
      'alignment:left',
      'alignment:center',
      'alignment:right',
      'alignment:justify',
      '|',
      'heading',
      'fontSize',
      '|',
      'fontColor',
      'fontBackgroundColor',
    ],
    fontSize: {
      options: [10, 12, 14, 16, 18, 20, 24],
    },
  };

  constructor(
    private http: HttpClient,
    private articleService: ArticleService,
    public datePipe: DatePipe,
    public dialogRef: MatDialogRef<ArticleDialogComponent>,
    private snackBar: MatSnackBar,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: LocalData,
  ) {
    this.localData = { ...data };
    this.action = this.localData.action;
    dialogRef.disableClose = true;
    this.articleImage = this.localData.articleImage;
    this.thumbnail = this.localData.thumbnail;
  }

  ngOnInit(): void {}

  doAction(): void {
    if (!this.localData) {
      return;
    }

    const expression = this.localData.action;
    const dataPass = {
      articleData: this.localData,
      uploadThumbnailImage: this.thumbnailUpload,
      uploadArticleImage: this.articleImgUpload,
    };

    switch (expression) {
      case Actions.ADD_ARTICLE:
        this.addArticle(dataPass);
        break;
      case Actions.UPDATE_ARTICLE:
        this.updateArticle(dataPass);
        break;
      case Actions.DELETE_ARTICLE:
        this.deleteArticle(this.localData);
        break;
    }
  }

  publish() {
    this.localData.published = true;
    this.publishClick = true;
    this.doAction();
  }

  draft() {
    this.localData.published = false;
    this.draftClick = true;
    this.doAction();
  }

  addArticle(data: any): void {
    try {
      if (this.publishClick) {
        this.loading = true;
      }
      if (this.draftClick) {
        this.draftLoading = true;
      }
      this.articleService
        .createArticle(data.articleData, data.uploadThumbnailImage, data.uploadArticleImage)
        .subscribe({
          next: (res: IAPIResponse) => {
            if (this.draftClick) {
              this.draftLoading = false;
            }

            if (this.publishClick) {
              this.loading = false;
            }

            if (!res.status) {
              if (this.publishClick) {
                this.loading = false;
              }

              if (this.draftClick) {
                this.draftLoading = false;
              }

              this.snackBar.open(res.message, 'ok', {
                duration: 6000,
              });
              return 'error';
            }

            this.snackBar.open(res.message, 'ok', {
              duration: 1000,
            });
            this.dialogRef.close();

            return 'success';
          },
          error: (err) => {
            if (this.publishClick) {
              this.loading = false;
            }

            if (this.draftClick) {
              this.draftLoading = false;
            }
            this.snackBar.open(err.error.message, 'ok', { duration: 6000 });
            return 'error';
          },
        });
    } catch (err) {
      if (this.publishClick) {
        this.loading = false;
      }

      if (this.draftClick) {
        this.draftLoading = false;
      }
      console.error(err);
    }
  }

  selectImage(event: Event): void {
    const element = event.currentTarget as HTMLInputElement;
    let fileList: FileList | null = element.files;
    if (!fileList || fileList.length === 0) {
      return;
    }

    const mimeType = fileList[0].type;
    if (mimeType.match(/image\/*/) == null) {
      return;
    }

    const reader = new FileReader();
    reader.readAsDataURL(fileList[0]);
    reader.onload = (_event) => {
      this.localData!.thumbnail = reader.result as unknown as string;
    };
    this.thumbnailUpload = <File>fileList[0];
  }

  selectArticleImage(event: Event): void {
    const element = event.currentTarget as HTMLInputElement;
    let fileList: FileList | null = element.files;
    if (!fileList || fileList.length === 0) {
      return;
    }

    const mimeType = fileList[0].type;
    if (mimeType.match(/image\/*/) == null) {
      return;
    }

    const reader = new FileReader();
    reader.readAsDataURL(fileList[0]);
    reader.onload = (_event) => {
      this.localData!.articleImage = reader.result as unknown as string;
    };
    this.articleImgUpload = <File>fileList[0];
  }

  closeDialog(): void {
    this.dialogRef.close({ event: 'Cancel' });
  }

  updateArticle(data: any) {
    try {
      if (this.publishClick) {
        this.loading = true;
      }
      if (this.draftClick) {
        this.draftLoading = true;
      }

      this.articleService
        .updateArticle(data.articleData, data.uploadThumbnailImage, data.uploadArticleImage)
        .subscribe({
          next: (res) => {
            let response = <IAPIResponse>res;
            if (response.status === false) {
              if (this.publishClick) {
                this.loading = false;
              }

              if (this.draftClick) {
                this.draftLoading = false;
              }
              this.snackBar.open(response.message, 'ok', {
                duration: 6000,
              });
              return 'error';
            }

            if (this.draftClick) {
              this.draftLoading = false;
            }

            if (this.publishClick) {
              this.loading = false;
            }
            this.snackBar.open(response.message, 'ok', { duration: 1000 });
            this.dialogRef.close();
            return 'success';
          },
          error: (err) => {
            if (this.publishClick) {
              this.loading = false;
            }

            if (this.draftClick) {
              this.draftLoading = false;
            }
          },
        });
    } catch (err) {
      if (this.publishClick) {
        this.loading = false;
      }

      if (this.draftClick) {
        this.draftLoading = false;
      }
      console.error(err);
    }
  }

  deleteArticle(data: IArticleCollection) {
    try {
      this.loading = true;
      this.articleService.deleteArticle(data._id).subscribe({
        next: (res) => {
          let response = <IAPIResponse>res;
          if (response.status === false) {
            this.loading = false;
            this.snackBar.open(response.message, 'ok', { duration: 6000 });
            return 'error';
          }
          this.loading = false;
          this.snackBar.open(response.message, 'ok', { duration: 1000 });
          this.dialogRef.close();
          return 'success';
        },
        error: (err) => {
          this.loading = false;
        },
      });
    } catch (err) {
      this.loading = false;
      console.error(err);
    }
  }

  clearUploads(type: string) {
    if (type === 'thumbnail') {
      if (this.action === 'Update Article') {
        this.localData.thumbnail = this.thumbnail;
        this.thumbnailUpload = undefined as unknown as File;
        return;
      }

      this.localData.thumbnail = '';
      this.thumbnailUpload = undefined as unknown as File;
      return;
    }

    if (this.action === 'Update Article') {
      this.localData.articleImage = this.articleImage;
      this.articleImgUpload = undefined as unknown as File;
      return;
    }

    this.localData.articleImage = '';
    this.articleImgUpload = undefined as unknown as File;
  }
}
