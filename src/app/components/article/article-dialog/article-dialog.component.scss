.cke_bottom {
  display: none !important;
}
.clear-image {
  padding: 0px !important;
  color: red;
}
/* Two-column responsive layout */
.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.form-col {
  flex: 1 1 50%;
  box-sizing: border-box;
}

.form-col-full {
  flex: 1 1 100%;
  box-sizing: border-box;
}

/* Upload section layout */
.upload-section {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.upload-image-col {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-info {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

/* Image styles */
.upload-image {
  width: 100px;
  height: 100px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid #ccc;
}

.clear-image {
  margin-top: 8px;
}

.input-file-button {
  position: relative;
  overflow: hidden;
}

.input-file-button input {
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.image-informative-cls {
  font-size: 12px;
  color: #666;
}

.cke_notification_warning {
  display: none !important;
}

/* Responsive */
@media (max-width: 960px) {
  .form-col {
    flex: 1 1 100%;
  }
}
