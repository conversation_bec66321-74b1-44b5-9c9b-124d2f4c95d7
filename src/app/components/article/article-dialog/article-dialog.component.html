<h2 class="font-medium" mat-dialog-title>
  <strong>{{ action }}</strong>
</h2>
<div class="pb-3" *ngIf="action === 'Add Article' || action === 'Update Article'; else elseTemplate">
  <form #articleForm="ngForm">
    <mat-dialog-content>
      <div fxLayout="row wrap" class="align-items-center">
        <div fxFlex="100" fxFlex.gt-md="50">
          <div class="m-r-15 m-l-15">
            <div class="d-flex align-items-center m-b-15">
              <div fxLayout="column">
                <img
                  class="upload-image"
                  [src]="localData.thumbnail ? localData.thumbnail : 'assets/images/logos/logo.png'" />
                <button
                  class="clear-image"
                  *ngIf="thumbnailUpload"
                  matSuffix
                  mat-raised-button
                  aria-label="Clear"
                  (click)="clearUploads('thumbnail')">
                  <mat-icon>highlight_off</mat-icon>Clear
                </button>
              </div>
              <div>
                <div fxLayout="columns">
                  <button mat-raised-button color="primary" class="m-l-15 input-file-button">
                    <label for="images">Select thumbnail </label>
                    <input
                      type="file"
                      id="images"
                      accept="image/png, image/jpeg, image/jpg"
                      required
                      (change)="selectImage($event)" />
                  </button>
                </div>
                <label fxLayout="columns" class="image-informative-cls m-t-10">Acceptable type: png, jpg, jpeg </label>
                <label fxLayout="columns" class="image-informative-cls">Upload limit: 15MB </label>
                <label fxLayout="columns" class="image-informative-cls">Width: 100px Height: 100px </label>
              </div>
            </div>
          </div>
        </div>
        <div fxFlex="100" fxFlex.gt-md="50">
          <div class="m-r-15 m-l-15">
            <div class="d-flex align-items-center m-b-15">
              <div fxLayout="column">
                <img
                  class="upload-image"
                  [src]="localData.articleImage"
                  onerror="this.src= 'assets/images/logos/logo.png'" />
                <button
                  class="clear-image"
                  *ngIf="articleImgUpload"
                  matSuffix
                  mat-raised-button
                  aria-label="Clear"
                  (click)="clearUploads('image')">
                  <mat-icon>highlight_off</mat-icon>Clear
                </button>
              </div>
              <div class="m-t-5">
                <div fxLayout="columns" class="m-t-20">
                  <button mat-raised-button color="accent" class="m-l-15 input-file-button">
                    <div>
                      <label for="media">Select article image </label>
                      <input
                        type="file"
                        id="images"
                        accept="image/png, image/jpeg, image/jpg"
                        required
                        (change)="selectArticleImage($event)" />
                    </div>
                  </button>
                </div>
                <label fxLayout="columns" class="image-informative-cls m-t-10">Acceptable type: png, jpg, jpeg </label>
                <label fxLayout="columns" class="image-informative-cls">Upload limit: 15MB </label>
                <label fxLayout="columns" class="image-informative-cls">Width: 200px Height: 200px </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div fxLayout="row wrap" class="align-items-center">
        <div fxFlex="100" fxFlex.gt-md="100">
          <div class="m-r-15 m-l-15">
            <mat-form-field>
              <input
                type="text"
                matInput
                required
                id="title"
                name="title"
                [(ngModel)]="localData.title"
                placeholder="Title"
                #title="ngModel"
                #input
                maxlength="100" />
              <mat-hint align="end">{{ input.value?.length || 0 }}/100</mat-hint>
            </mat-form-field>
          </div>
        </div>
        <div fxFlex="100" fxFlex.gt-md="100">
          <div class="m-r-15 m-l-15">
            <mat-form-field>
              <input
                type="text"
                matInput
                required
                id="author"
                name="author"
                placeholder="Author"
                required
                #author="ngModel"
                [(ngModel)]="localData.author"
                #inputAuthor
                maxlength="26" />
              <mat-hint align="end">{{ inputAuthor.value?.length || 0 }}/26</mat-hint>
            </mat-form-field>
          </div>
        </div>
        <div fxFlex="100" fxFlex.gt-md="100">
          <div class="m-r-15 m-l-15">
            <mat-card class="m-b-5 m-l-5">
              <mat-card-content>
                <mat-card-subtitle class="m-b-20">Article</mat-card-subtitle>
                <ckeditor
                  required
                  id="editor"
                  name="editor"
                  #editor="ngModel"
                  [(ngModel)]="localData.articleData"
                  editorUrl="https://cdn.ckeditor.com/4.14.0/full/ckeditor.js"
                  [config]="ckEditorConfig"></ckeditor>
              </mat-card-content>
            </mat-card>
          </div>
        </div>
      </div>
    </mat-dialog-content>
    <mat-dialog-actions>
      <button
        mat-button
        (click)="publish()"
        mat-flat-button
        color="primary"
        [class.spinner]="loading"
        [disabled]="!articleForm.valid || loading">
        <span *ngIf="action === 'Add Article'">Publish</span>
        <span *ngIf="action === 'Update Article' && !localData.published"> Update and publish</span>
        <span *ngIf="action === 'Update Article' && localData.published"> Update</span>
      </button>
      <button
        *ngIf="!localData.published || action === 'Add Article'"
        mat-flat-button
        (click)="draft()"
        [class.spinner]="draftLoading"
        [disabled]="!articleForm.valid || draftLoading">
        Save as draft
      </button>
      <button mat-button (click)="closeDialog()">Cancel</button>
    </mat-dialog-actions>
  </form>
</div>
<ng-template #elseTemplate>
  <p>
    Sure to {{ action }} <b>{{ localData.title }}</b> ?
  </p>
  <div mat-dialog-actions align="center" class="pt-3">
    <button
      mat-button
      (click)="doAction()"
      mat-flat-button
      color="primary"
      [class.spinner]="loading"
      [disabled]="loading">
      {{ action }}
    </button>
    <button mat-button (click)="closeDialog()">Cancel</button>
  </div>
</ng-template>
