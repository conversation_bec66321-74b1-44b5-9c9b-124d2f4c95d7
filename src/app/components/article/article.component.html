<div *ngIf="isLoading" fxLayout="row " fxLayoutAlign="center center">
  <app-spinner></app-spinner>
</div>

<div fxLayout="row wrap">
  <div fxFlex="100">
    <mat-card>
      <mat-card-content>
        <div fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="20px">
          <div fxFlex.gt-md="25" fxFlex.gt-lg="25" fxFlex="100">
            <mat-form-field>
              <input
                matInput
                placeholder="Search Article"
                autocomplete="off"
                (keyup.enter)="searchArticleByTitle()"
                [(ngModel)]="searchArticle" />
              <button
                *ngIf="searchArticle"
                mat-button
                matSuffix
                mat-icon-button
                aria-label="Clear"
                (click)="clearSearch()">
                <mat-icon>close</mat-icon>
              </button>
            </mat-form-field>
          </div>
          <div fxFlex.gt-md="30%" fxFlex.gt-md="30%" fxLayoutAlign="end">
            <button mat-flat-button color="accent" (click)="openArticleDialog('Add Article', {})">
              <i-feather name="plus-circle" class="feather-15"></i-feather>
              Add Article
            </button>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
<h2 class="mat-h2 text-center m-t-10" *ngIf="noArticle">{{ msg | titlecase }} !</h2>
<div fxLayout="row wrap" class="article-content">
  <div fxFlex.gt-sm="33.33%" fxFlex="100" *ngFor="let article of obs | async">
    <mat-card *ngIf="!noArticle" class="list-card-custom">
      <div fxFlex="40">
        <div class="mat-ripple-style">
          <div>
            <img
              class="list-card-left-image-custom"
              src="{{ article.thumbnail }}"
              alt="Thumbnail of Article"
              onerror="this.src='assets/images/backgrounds/default-thumbnail.jpeg'" />
          </div>
        </div>
      </div>
      <div fxFlex="60">
        <mat-card-content class="mat-card-content-custom">
          <mat-card-title class="mat-subheading-1" [title]="article.title">
            <span class="title-pub-cls"> {{ article.shortTitle | titlecase }}</span>
          </mat-card-title>
          <mat-card-subtitle
            class="m-b-0 m-t-10 text-muted fw-medium"
            fxLayoutAlign="space-between center"
            fxLayoutAlign.lt-sm="space-between center">
            {{ article.shortAuthor | titlecase }}
          </mat-card-subtitle>
          <mat-card-subtitle class="m-t-10">
            {{ article.createdAt | date: ' MMM d, y ' }}
          </mat-card-subtitle>
          <mat-card-subtitle class="text-muted fw-medium">
            Published:
            <span *ngIf="article.published; else notPublished" class="text-success feather-18"> YES</span>
            <ng-template #notPublished>
              <span class="text-danger feather-18">NO</span>
            </ng-template>
          </mat-card-subtitle>
        </mat-card-content>

        <mat-card-footer class="article-card-footer text-right">
          <div>
            <div>
              <a (click)="openArticleDialog('Update Article', article)" class="m-r-10 cursor-pointer" title="Edit">
                <i-feather name="edit" class="text-primary feather-18"></i-feather>
              </a>
              <a (click)="openArticleDialog('Delete Article', article)" class="ml-auto cursor-pointer" title="Delete">
                <i-feather name="trash" class="text-danger feather-18"></i-feather>
              </a>
            </div>
          </div>
        </mat-card-footer>
      </div>
    </mat-card>
  </div>
</div>
<mat-card>
  <div align="end">
    <mat-paginator
      #paginator
      [length]="totalRows"
      [pageIndex]="currentPage"
      [pageSize]="pageSize"
      (page)="pageChanged($event)"
      [pageSizeOptions]="[12, 24, 36, 48]"
      aria-label="Select page">
    </mat-paginator>
  </div>
</mat-card>
