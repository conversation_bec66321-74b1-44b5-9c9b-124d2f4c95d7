<!-- Loader -->
<div *ngIf="isLoading" class="flex-center">
  <app-spinner></app-spinner>
</div>

<!-- Search + Add Article -->
<div class="article-header">
  <div class="article-search">
    <mat-card>
      <mat-card-content>
        <div class="article-search-row">
          <div class="search-input">
            <mat-form-field>
              <input
                matInput
                placeholder="Search Article"
                autocomplete="off"
                (keyup.enter)="searchArticleByTitle()"
                [(ngModel)]="searchArticle" />
              <button
                *ngIf="searchArticle"
                mat-button
                matSuffix
                mat-icon-button
                aria-label="Clear"
                (click)="clearSearch()">
                <mat-icon>close</mat-icon>
              </button>
            </mat-form-field>
          </div>
          <div class="add-btn">
            <button mat-flat-button color="accent" (click)="openArticleDialog('Add Article', {})">
              <i-feather name="plus-circle" class="feather-15"></i-feather>
              Add Article
            </button>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>

<h2 class="mat-h2 text-center m-t-10" *ngIf="noArticle">{{ msg | titlecase }} !</h2>

<!-- Articles Grid -->
<div class="article-grid">
  <div class="article-item" *ngFor="let article of obs | async">
    <mat-card *ngIf="!noArticle" class="list-card-custom">
      <div class="article-card">
        <div class="article-thumbnail">
          <img
            class="list-card-left-image-custom"
            [src]="article.thumbnail"
            alt="Thumbnail of Article"
            onerror="this.src='assets/images/backgrounds/default-thumbnail.jpeg'" />
        </div>

        <div class="article-details">
          <mat-card-content class="mat-card-content-custom">
            <mat-card-title class="mat-subheading-1" [title]="article.title">
              <span class="title-pub-cls">{{ article.shortTitle | titlecase }}</span>
            </mat-card-title>

            <mat-card-subtitle class="m-b-0 m-t-10 text-muted fw-medium">
              {{ article.shortAuthor | titlecase }}
            </mat-card-subtitle>

            <mat-card-subtitle class="m-t-10">
              {{ article.createdAt | date: 'MMM d, y' }}
            </mat-card-subtitle>

            <mat-card-subtitle class="text-muted fw-medium">
              Published:
              <span *ngIf="article.published; else notPublished" class="text-success feather-18">YES</span>
              <ng-template #notPublished>
                <span class="text-danger feather-18">NO</span>
              </ng-template>
            </mat-card-subtitle>
          </mat-card-content>

          <mat-card-footer class="article-card-footer">
            <div class="article-actions">
              <a (click)="openArticleDialog('Update Article', article)" class="m-r-10 cursor-pointer" title="Edit">
                <i-feather name="edit" class="text-primary feather-18"></i-feather>
              </a>
              <a (click)="openArticleDialog('Delete Article', article)" class="cursor-pointer" title="Delete">
                <i-feather name="trash" class="text-danger feather-18"></i-feather>
              </a>
            </div>
          </mat-card-footer>
        </div>
      </div>
    </mat-card>
  </div>
</div>

<!-- Pagination -->
<mat-card>
  <div class="text-right">
    <mat-paginator
      #paginator
      [length]="totalRows"
      [pageIndex]="currentPage"
      [pageSize]="pageSize"
      (page)="pageChanged($event)"
      [pageSizeOptions]="[12, 24, 36, 48]"
      aria-label="Select page">
    </mat-paginator>
  </div>
</mat-card>
