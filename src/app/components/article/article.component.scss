.mat-ripple-style {
  height: 200px;
}

.article-card-footer {
  margin: 0px 20px 0px 0px !important;
}

.article-content {
  min-height: 60vh;
}

/* Center the loader */
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Header section */
.article-search-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.search-input {
  flex: 1 1 60%;
  min-width: 250px;
}

.add-btn {
  flex: 1 1 auto;
  display: flex;
  justify-content: flex-end;
}

/* Articles grid layout */
.article-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 20px;
}

.article-item {
  flex: 1 1 calc(33.33% - 20px);
  box-sizing: border-box;
}

@media (max-width: 960px) {
  .article-item {
    flex: 1 1 calc(50% - 20px);
  }
}

@media (max-width: 600px) {
  .article-item {
    flex: 1 1 100%;
  }
}

/* Article card layout */
.article-card {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.article-thumbnail {
  flex: 0 0 40%;
}

.article-details {
  flex: 1 1 60%;
}

/* Footer actions */
.article-card-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 10px;
}

.article-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}
