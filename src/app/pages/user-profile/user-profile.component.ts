import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, FormGroupDirective, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AdminService } from 'src/app/services/admin.service';
import { ProfileService } from 'src/app/services/profile.service';
import { IAPIResponse } from 'src/app/types/interfaces/api.interface';
import { IUserCollection } from 'src/app/types/interfaces/user.interface';
import Validation from 'src/app/validators/confirmed.validator';

@Component({
  selector: 'app-user-profile',
  templateUrl: './user-profile.component.html',
  styleUrls: ['./user-profile.component.scss'],
})
export class UserProfileComponent implements OnInit {
  profileForm: FormGroup = new FormGroup({});
  passwordForm: FormGroup = new FormGroup({});
  //carouselForm: FormGroup = new FormGroup({});

  userDataSubmitted = false;
  passwordDataSubmitted = false;
  //carouselDataSubmitted = false;

  userId: string = '';
  editFormLoading: boolean = false;
  pwdFormLoading: boolean = false;

  //images!: string;
  //sliderImage!: string;
  //imageUpload: File[] = [];
  //carouselTitle: String = '';
  //carouselDescription: String = '';

  profileFormData!: IUserCollection;
  passwordFormData!: IUserCollection;
  //carouselFormData!: ICarousel;

  isLoading: boolean = false;

  constructor(
    private profileService: ProfileService,
    private adminService: AdminService,
    private fb: FormBuilder,
    public snackBar: MatSnackBar,
  ) {
    this.userId = this.adminService.getAdminId() || '';
  }

  ngOnInit(): void {
    this.profileForm = this.fb.group({
      name: [''],
      email: ['', Validators.email],
      mobile: ['', [Validators.pattern('^((\\+91-?)|0)?[0-9]{10}$')]],
    });

    this.passwordForm = this.fb.group(
      {
        newPassword: ['', [Validators.required, Validators.minLength(6)]],
        confirmNewPassword: ['', Validators.required],
      },
      {
        validators: [Validation.match('newPassword', 'confirmNewPassword')],
      },
    );

    this.isLoading = true;
    this.profileService.getUserById(this.userId).subscribe((res: Object) => {
      if (!res) {
        this.isLoading = false;
        return;
      }
      this.isLoading = false;

      let response = <IUserCollection>res;
      this.profileForm.patchValue({
        name: response.name,
        email: response.email,
        mobile: response.mobile,
      });
    });

    /*this.carouselForm = new FormGroup({
      title: new FormControl('', [Validators.required]),
      description: new FormControl('', [Validators.required]),
      file: new FormControl('', [Validators.required]),
      fileSource: new FormControl('', [Validators.required]),
    });

    this.getCarouselData();*/
  }

  get editFormControl(): { [key: string]: AbstractControl } {
    return this.profileForm.controls;
  }
  get passwordFormControl(): { [key: string]: AbstractControl } {
    return this.passwordForm.controls;
  }
  /*get c(): { [key: string]: AbstractControl } {
    return this.carouselForm.controls;
  } */

  editFormSubmit() {
    try {
      this.userDataSubmitted = true;
      if (this.profileForm.invalid) {
        return;
      }

      this.profileFormData = this.profileForm.value;
      if (this.profileForm.value) {
        this.editFormLoading = true;
      }

      this.profileService.updateUserDetails(this.userId, this.profileFormData).subscribe({
        next: (res) => {
          let response = <IAPIResponse>res;
          if (!response.status) {
            this.editFormLoading = false;
            this.openSnackBar(response.message, 'Try again');
            return;
          }

          this.editFormLoading = false;
          this.profileService.updateAdminName('form submitted');
          this.openSnackBar('Profile Updated', 'Success');
        },
        error: (err) => {
          this.editFormLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (error) {
      console.error(error);
      this.editFormLoading = false;
      this.openSnackBar('Something went wrong', 'Try again');
    }
  }

  passwordFormSubmit(formDirective: FormGroupDirective) {
    try {
      this.passwordDataSubmitted = true;
      if (this.passwordForm.invalid) {
        return;
      }

      this.passwordFormData = this.passwordForm.value;
      if (this.passwordForm.value) {
        this.pwdFormLoading = true;
      }

      this.profileService.updateUserPassword(this.userId, this.passwordFormData).subscribe({
        next: (res) => {
          this.passwordDataSubmitted = false;
          if (!res.status) {
            this.pwdFormLoading = false;
            this.openSnackBar(res.message, 'Try again');
            return;
          }

          this.pwdFormLoading = false;
          this.openSnackBar('Password Updated', 'Success');
          formDirective.resetForm();
        },
        error: (err) => {
          this.pwdFormLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (error) {
      console.error(error);
      this.pwdFormLoading = false;
      this.openSnackBar('Something went wrong', 'Try again');
    }
  }

  /* carouselSubmit() {
    this.carouselDataSubmitted = true;
    if (this.carouselForm.invalid) {
      return;
    }

    this.carouselFormData = this.carouselForm.value;
    this.profileService.carouselUpload(this.carouselFormData, this.imageUpload).subscribe((res) => {
      if (!res.status) {
        this.openSnackBar(res.message, 'Try again');
        return;
      }

      this.openSnackBar('Successfully Carousel Added', 'Success');
      this.getCarouselData();
    });
  } */

  openSnackBar(message: string, action: string) {
    this.snackBar.open(message, action, {
      duration: 1000,
    });
  }

  /* onFileChange(event) {
    const reader = new FileReader();

    if (event.target.files && event.target.files.length) {
      const [file] = event.target.files;
      reader.readAsDataURL(file);
      reader.onload = () => {
        this.images = reader.result as string;
        this.carouselForm.patchValue({
          fileSource: reader.result,
        });
      };

      this.imageUpload = event.target.files;
    }
  } */

  /* getCarouselData() {
    this.profileService.getCarouselData().subscribe((res) => {
      if (!res) {
        return;
      }

      let carouseResponse = <ICarousel>res.data[0];
      this.carouselForm.patchValue({
        title: carouseResponse.title,
        description: carouseResponse.description,
      });

      this.images = res.data[0].image;
      this.sliderImage = res.data[0].image;
      this.carouselDescription = res.data[0].description;
    });
  } */
}
