<div *ngIf="isLoading" fxLayout="row " fxLayoutAlign="center center">
  <app-spinner></app-spinner>
</div>
<div fxLayout="row wrap">
  <div fxFlex="100%">
    <mat-card class="m-b-0">
      <mat-card-content> <mat-card-title>Account Settings</mat-card-title></mat-card-content>
    </mat-card>
    <div fxLayout="row wrap">
      <div fxFlex="100%">
        <mat-card fxFlex.gt-sm="50%">
          <mat-card-content>
            <mat-card-title class="m-b-25">Edit Profile</mat-card-title>
            <form [formGroup]="profileForm" class="basic-form" (ngSubmit)="editFormSubmit()">
              <div fxLayout="row wrap">
                <div fxFlex.gt-md="100" fxFlex="100">
                  <mat-form-field>
                    <input matInput formControlName="name" placeholder="Name" />
                  </mat-form-field>
                </div>
                <div fxFlex.gt-md="100" fxFlex="100">
                  <mat-form-field>
                    <input
                      type="email"
                      matInput
                      formControlName="email"
                      placeholder="Email"
                      [ngClass]="{ 'is-invalid': userDataSubmitted && editFormControl['email'].errors }" />
                  </mat-form-field>
                  <div
                    *ngIf="userDataSubmitted && editFormControl['email'].errors"
                    class="help-block text-danger m-b-5">
                    <div *ngIf="editFormControl['email'].errors['email']">Email is invalid</div>
                  </div>
                </div>
                <div fxFlex.gt-md="100" fxFlex="100">
                  <mat-form-field>
                    <input
                      matInput
                      type="text"
                      formControlName="mobile"
                      placeholder="Mobile"
                      maxlength="10"
                      [ngClass]="{ 'is-invalid': userDataSubmitted && editFormControl['mobile'].errors }"
                      oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');" />
                  </mat-form-field>
                  <div *ngIf="editFormControl['mobile'].invalid" class="help-block text-danger m-b-5">
                    <div>Please, Enter 10 digit Mobile Number</div>
                  </div>
                </div>
                <div fxFlex.gt-sm="100" fxFlex="100">
                  <button [class.spinner]="editFormLoading && profileForm.valid" mat-raised-button color="primary">
                    Update
                  </button>
                </div>
              </div>
            </form>
          </mat-card-content>
        </mat-card>
        <mat-card fxFlex.gt-sm="50%">
          <mat-card-content>
            <mat-card-title class="m-b-25">Update Password</mat-card-title>
            <form
              [formGroup]="passwordForm"
              class="basic-form"
              #formDirective="ngForm"
              (ngSubmit)="passwordFormSubmit(formDirective)">
              <div fxLayout="row wrap">
                <div fxFlex.gt-sm="100" fxFlex="100">
                  <mat-form-field>
                    <input
                      matInput
                      formControlName="newPassword"
                      placeholder="New Password"
                      type="password"
                      [ngClass]="{
                        'is-invalid': passwordDataSubmitted && passwordFormControl['newPassword'].errors
                      }" />
                  </mat-form-field>
                  <div
                    *ngIf="passwordDataSubmitted && passwordFormControl['newPassword'].errors"
                    class="invalid-feedback">
                    <div *ngIf="passwordFormControl['newPassword'].errors['required']">New Password is required</div>
                    <div *ngIf="passwordFormControl['newPassword'].errors['minlength']">
                      Password must be at least 6 characters
                    </div>
                  </div>
                </div>
                <div fxFlex.gt-sm="100" fxFlex="100">
                  <mat-form-field>
                    <input
                      matInput
                      formControlName="confirmNewPassword"
                      placeholder="Confirm New Password"
                      type="password"
                      [ngClass]="{
                        'is-invalid': passwordDataSubmitted && passwordFormControl['confirmNewPassword'].errors
                      }" />
                  </mat-form-field>
                  <div
                    *ngIf="passwordDataSubmitted && passwordFormControl['confirmNewPassword'].errors"
                    class="invalid-feedback">
                    <div *ngIf="passwordFormControl['confirmNewPassword'].errors['required']">
                      Confirm New Password is required
                    </div>
                    <div *ngIf="passwordFormControl['confirmNewPassword'].errors['matching']">
                      New Password and Confirm New password not match
                    </div>
                  </div>
                </div>
                <div fxFlex.gt-sm="100" fxFlex="100">
                  <button mat-raised-button [class.spinner]="pwdFormLoading && passwordForm.valid" color="primary">
                    Update
                  </button>
                </div>
              </div>
            </form>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </div>
</div>
