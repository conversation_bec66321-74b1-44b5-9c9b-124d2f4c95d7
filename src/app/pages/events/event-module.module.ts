import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { FeatherModule } from 'angular-feather';
import { allIcons } from 'angular-feather/icons';
import { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';
import { EventComponent } from 'src/app/components/event/event.component';
import { SpinnerModule } from 'src/app/components/spinner/spinner.module';
import { MaterialModule } from 'src/app/material/material.module';

import { eventRoutes } from './event.routing';
import { EventsComponent } from './events.component';

@NgModule({
  declarations: [EventsComponent, EventComponent],
  imports: [
    CommonModule,
    RouterModule.forChild(eventRoutes),
    CommonModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    FlexLayoutModule,
    FeatherModule.pick(allIcons),
    NgxDatatableModule,
    SpinnerModule,
    NgxMaterialTimepickerModule,
  ],
})
export class EventModuleModule {}
