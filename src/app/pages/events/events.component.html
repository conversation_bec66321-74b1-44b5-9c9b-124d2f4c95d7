<div *ngIf="isPageLoading" fxLayout="row " fxLayoutAlign="center center">
  <app-spinner></app-spinner>
</div>

<div fxLayout="row wrap">
  <div fxFlex.gt-sm="100" fxFlex="100">
    <mat-card>
      <mat-card-content
        ><mat-card-title>Create Event</mat-card-title>
        <form
          #formDirective="ngForm"
          [formGroup]="eventForm"
          class="basic-form"
          (ngSubmit)="eventFormSubmit(formDirective)">
          <div fxLayout="row wrap">
            <!-- <div fxFlex.gt-md="25" fxFlex="100">
              <img
                class="img-event"
                width="150"
                height="150"
                [src]="img"
                onerror="this.src='assets/images/backgrounds/thumbnail-dummy-image.svg'" />
            </div> -->
            <div fxFlex.gt-md="75" fxFlex="100">
              <div>
                <mat-form-field>
                  <input
                    matInput
                    formControlName="title"
                    placeholder="Title"
                    name="title"
                    [ngClass]="{
                      'is-invalid': eventDataSubmitted && eventFormControl['title'].errors
                    }" />
                </mat-form-field>
                <div *ngIf="eventDataSubmitted && eventFormControl['title'].errors" class="invalid-feedback">
                  <div *ngIf="eventFormControl['title'].errors['required']">Title is required</div>
                </div>
              </div>

              <div>
                <mat-form-field>
                  <input
                    matInput
                    formControlName="description"
                    name="description"
                    placeholder="Description"
                    [ngClass]="{
                      'is-invalid': eventDataSubmitted && eventFormControl['description'].errors
                    }" />
                </mat-form-field>
                <div *ngIf="eventDataSubmitted && eventFormControl['description'].errors" class="invalid-feedback">
                  <div *ngIf="eventFormControl['description'].errors['required']">Description is required</div>
                </div>
              </div>

              <div style="display: flex">
                <div fxLayout="column">
                  <img
                    class="img-event"
                    [src]="img"
                    onerror="this.src='assets/images/backgrounds/thumbnail-dummy-image.svg'" />
                  <button
                    class="clear-image"
                    *ngIf="imageUpload"
                    matSuffix
                    mat-raised-button
                    aria-label="Clear"
                    (click)="clearUploads()">
                    <mat-icon>highlight_off</mat-icon>Clear
                  </button>
                </div>
                <div style="margin-top: 80px">
                  <div fxLayout="columns">
                    <button mat-raised-button color="accent">
                      <label for="images">Select a event image</label>
                      <input
                        type="file"
                        id="images"
                        accept="image/png, image/jpeg, image/jpg"
                        required
                        (change)="selectImage($event)" />
                    </button>
                  </div>
                  <label fxLayout="columns" class="image-informative-cls m-l-5 m-t-10"
                    >Acceptable type: png, jpg, jpeg
                  </label>
                  <label fxLayout="columns" class="image-informative-cls m-l-5">Upload limit: 15MB </label>
                  <label fxLayout="columns" class="image-informative-cls m-l-5">Width: 200px Height: 200px </label>
                </div>
              </div>

              <div>
                <mat-form-field style="width: 25%">
                  <mat-label>Event Date</mat-label>
                  <input
                    matInput
                    [readonly]="true"
                    [min]="todayDate"
                    [matDatepicker]="picker"
                    autocomplete="off"
                    (ngModelChange)="getDate($event)"
                    formControlName="eventSelectDate" />
                  <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                  <mat-datepicker #picker color="primary"></mat-datepicker>
                </mat-form-field>
              </div>

              <div *ngIf="eventDate">
                <div style="padding: 10px; width: 30%">
                  <mat-label>Time Start</mat-label>
                  <ngx-timepicker-field (timeChanged)="timeFieldFrom($event)"></ngx-timepicker-field>
                </div>
                <div style="padding: 10px; width: 30%">
                  <mat-label>Time End</mat-label>
                  <ngx-timepicker-field (timeChanged)="timeFieldTo($event)"></ngx-timepicker-field>
                </div>
              </div>

              <div class="m-t-15" style="margin: 7px">
                <button
                  mat-raised-button
                  [class.spinner]="buttonLoading"
                  [disabled]="!eventForm.valid || buttonLoading"
                  color="primary">
                  Submit
                </button>
              </div>
            </div>
          </div>
        </form>
      </mat-card-content></mat-card
    >
  </div>
</div>

<div fxLayout="row wrap">
  <div fxFlex="100">
    <mat-card>
      <mat-card-content>
        <div fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="40px">
          <div fxFlex.gt-md="10%" fxFlex.gt-md="10%"><mat-card-title class="m-l-10">Events</mat-card-title></div>
        </div>
        <div class="table-responsive">
          <div *ngIf="emptyDataMsg; then eventEmpty; else tableBlock"></div>
          <ng-template #eventEmpty>
            <h3 *ngIf="emptyDataMsg" _ngcontent-tqm-c386="" class="mat-h3 text-center ng-star-inserted">
              Events Not Found
            </h3></ng-template
          >
          <ng-template #tableBlock
            ><table mat-table [dataSource]="dataSource" class="w-100">
              <ng-container matColumnDef="title">
                <th mat-header-cell *matHeaderCellDef>Title</th>
                <td mat-cell *matCellDef="let element">
                  <div class="d-flex align-items-center">
                    <img
                      class="img"
                      width="50"
                      height="50"
                      alt="event-image"
                      [src]="element.image"
                      onerror="this.src='assets/images/backgrounds/thumbnail-dummy-image.svg'" />

                    <div class="m-l-15">
                      <p class="lh-md m-t-0">
                        {{ element.title | titlecase }}
                      </p>
                    </div>
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="description">
                <th mat-header-cell *matHeaderCellDef>Description</th>
                <td mat-cell *matCellDef="let element">
                  <div class="d-flex align-items-center">
                    <p class="lh-md m-t-0">
                      {{ element.description | titlecase | slice: 0:50 }}
                    </p>
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="date">
                <th mat-header-cell *matHeaderCellDef>Date</th>
                <td mat-cell *matCellDef="let element">
                  <div class="d-flex align-items-center">
                    <p class="lh-md m-t-0">
                      {{ element.startDate | date: ' MMM d, y ' }}
                    </p>
                    <!--  {{ element.startDate | date: ' MMM d, y , hh:mm a' }} - {{ element.endDate | date: 'hh:mm a' }} -->
                  </div>
                </td>
              </ng-container>
              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef>Action</th>

                <td mat-cell *matCellDef="let element" class="action-link">
                  <!-- <a (click)="openDialog('Update Event', element)" class="m-r-10 cursor-pointer" title="Edit">
                    <i-feather name="edit" class="text-primary feather-18"></i-feather>
                  </a> -->

                  <a (click)="openDialog('Delete Event', element)" class="m-r-10 cursor-pointer" title="Delete Event">
                    <i-feather name="trash" class="text-danger feather-18"></i-feather>
                  </a>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr></table
          ></ng-template>

          <mat-paginator
            #paginator
            [length]="totalRows"
            [pageIndex]="currentPage"
            [pageSize]="pageSize"
            (page)="pageChanged($event)"
            [pageSizeOptions]="[10, 20, 30, 40]"
            aria-label="Select page">
          </mat-paginator>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
