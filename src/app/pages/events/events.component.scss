.img-event {
  width: 200px;
  height: 200px;
  margin: 10px 10px 0px 10px;
}
.clear-image {
  padding: 0px !important;
  color: red;
  margin: 0px 10px 10px 10px;
}
/* General Flex Utilities */
.flex-row-wrap {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-item {
  flex: 1 1 100%;
}

.flex-space-between-center {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.gap-40 {
  gap: 40px;
}

/* Responsive Layouts */
@media (min-width: 768px) {
  .flex-75-md {
    flex: 1 1 75%;
  }

  .flex-10-md {
    flex: 1 1 10%;
  }
}

/* Image & Upload Section */
.image-section {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: flex-start;
}

.image-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-controls {
  margin-top: 60px;
}

/* Time Fields */
.time-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.time-field {
  flex: 1 1 30%;
  padding: 10px;
}

/* Date Field */
.date-field {
  width: 25%;
}

/* Table Alignment */
.action-link {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
