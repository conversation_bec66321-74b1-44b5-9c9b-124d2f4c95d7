<div *ngIf="isPageLoading" fxLayout="row " fxLayoutAlign="center center">
  <app-spinner></app-spinner>
</div>

<div fxLayout="row wrap">
  <div fxFlex.gt-sm="100" fxFlex="100">
    <mat-card>
      <mat-card-content>
        <mat-card-title>Create Message</mat-card-title>
        <form
          #formDirective="ngForm"
          [formGroup]="messageForm"
          class="basic-form"
          (ngSubmit)="messageFormSubmit(formDirective)">
          <div fxLayout="row wrap">
            <div fxFlex.gt-md="75" fxFlex="100">
              <div>
                <mat-form-field>
                  <input
                    matInput
                    formControlName="title"
                    placeholder="Title"
                    name="title"
                    [ngClass]="{
                      'is-invalid': messageDataSubmitted && messageFormControl['title'].errors
                    }" />
                </mat-form-field>
                <div *ngIf="messageDataSubmitted && messageFormControl['title'].errors" class="invalid-feedback">
                  <div *ngIf="messageFormControl['title'].errors['required']">Title is required</div>
                </div>
              </div>
              <div>
                <mat-form-field>
                  <textarea
                    matInput
                    formControlName="content"
                    name="content"
                    placeholder="Message"
                    [ngClass]="{
                      'is-invalid': messageDataSubmitted && messageFormControl['content'].errors
                    }"></textarea>
                </mat-form-field>
                <div *ngIf="messageDataSubmitted && messageFormControl['content'].errors" class="invalid-feedback">
                  <div *ngIf="messageFormControl['content'].errors['required']">Message is required</div>
                </div>
              </div>
              <div class="m-t-15">
                <button
                  mat-raised-button
                  [class.spinner]="buttonLoading"
                  [disabled]="!messageForm.valid || buttonLoading"
                  color="primary">
                  Submit
                </button>
              </div>
            </div>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  </div>
</div>

<div fxLayout="row wrap">
  <div fxFlex="100">
    <mat-card>
      <mat-card-content>
        <div><mat-card-title class="m-l-10">Message Board</mat-card-title></div>
        <div class="table-responsive">
          <div *ngIf="emptyDataMsg; then messageEmpty; else tableBlock"></div>
          <ng-template #messageEmpty>
            <h3 *ngIf="emptyDataMsg" _ngcontent-tqm-c386="" class="mat-h3 text-center ng-star-inserted">
              Messages Not Found
            </h3></ng-template
          >
          <ng-template #tableBlock>
            <mat-card-content>
              <div class="responsive-table">
                <table mat-table [dataSource]="dataSource" multiTemplateDataRows>
                  <ng-container matColumnDef="title">
                    <th mat-header-cell *matHeaderCellDef>Title</th>
                    <td mat-cell *matCellDef="let messageElement">
                      <!-- {{ element | json }} -->
                      <div class="d-flex align-items-center" style="cursor: pointer">
                        <div class="m-l-15">
                          <p class="lh-md m-t-0">
                            {{ messageElement.title | titlecase }}
                          </p>
                        </div>
                      </div>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="message">
                    <th mat-header-cell *matHeaderCellDef>Message</th>
                    <td mat-cell *matCellDef="let messageElement">
                      <div class="d-flex align-items-center" style="cursor: pointer">
                        <p class="lh-md m-t-0" [appMapUrlFromText]="messageElement.content"></p>
                      </div>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="date">
                    <th mat-header-cell *matHeaderCellDef>Date</th>
                    <td mat-cell *matCellDef="let messageElement">
                      <div class="d-flex align-items-center" style="cursor: pointer">
                        <p class="lh-md m-t-0">
                          {{ messageElement.createdAt | date }}
                        </p>
                      </div>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="action">
                    <th mat-header-cell *matHeaderCellDef>Action</th>
                    <td mat-cell *matCellDef="let messageElement" class="action-link">
                      <a
                        (click)="openDialog('Delete Message', messageElement)"
                        class="m-r-10 cursor-pointer"
                        title="Delete Message">
                        <i-feather name="trash" class="text-danger feather-18"></i-feather>
                      </a>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="expandedDetail">
                    <td mat-cell *matCellDef="let messageElement" [attr.colspan]="displayedColumns.length">
                      <div
                        class="example-element-detail"
                        [@detailExpand]="messageElement === expandedElement ? 'expanded' : 'collapsed'">
                        <div
                          class="example-element-diagram"
                          style="margin-left: 40px; padding: 10px; margin-top: -28px">
                          <h2>Comments</h2>
                          <!-- <div class="example-element-position">{{ element.comments }}</div> -->
                          <div *ngIf="messageElement.comments.length === 0; then messageEmpty; else tableBlock"></div>
                          <ng-template #messageEmpty>
                            <h3 class="mat-h3 text-center ng-star-inserted" style="margin-left: 20px">
                              Comments Not Found
                            </h3></ng-template
                          >
                          <ng-template #tableBlock>
                            <table mat-table [dataSource]="messageElement.comments">
                              <ng-container matColumnDef="name">
                                <th mat-header-cell *matHeaderCellDef>Name</th>
                                <td mat-cell *matCellDef="let element">
                                  <div class="d-flex align-items-center">
                                    <p class="lh-md m-t-0">
                                      {{ element.name | titlecase }}
                                    </p>
                                  </div>
                                </td>
                              </ng-container>

                              <!-- Name Column -->
                              <ng-container matColumnDef="comment">
                                <th mat-header-cell *matHeaderCellDef>Comment</th>
                                <td mat-cell *matCellDef="let element">
                                  <div class="d-flex align-items-center">
                                    <p class="lh-md m-t-0" [appMapUrlFromText]="element.text"></p>
                                  </div>
                                </td>
                              </ng-container>

                              <!-- Weight Column -->
                              <ng-container matColumnDef="email">
                                <th mat-header-cell *matHeaderCellDef>Email</th>
                                <td mat-cell *matCellDef="let element">
                                  <div class="d-flex align-items-center">
                                    <p class="lh-md m-t-0">
                                      {{ element.email }}
                                    </p>
                                  </div>
                                </td>
                              </ng-container>
                              <ng-container matColumnDef="action">
                                <th mat-header-cell *matHeaderCellDef>Action</th>
                                <td mat-cell *matCellDef="let element" class="action-link">
                                  <a
                                    (click)="openDialog('Delete Comment', element, messageElement._id)"
                                    class="m-r-10 cursor-pointer"
                                    title="Delete Comment">
                                    <i-feather name="trash" class="text-danger feather-18"></i-feather>
                                  </a>
                                </td>
                              </ng-container>
                              <tr mat-header-row *matHeaderRowDef="commentColumns"></tr>
                              <tr mat-row *matRowDef="let row; columns: commentColumns"></tr>
                            </table>
                          </ng-template>
                        </div>
                      </div>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                  <tr
                    mat-row
                    *matRowDef="let element; columns: displayedColumns"
                    class="example-element-row"
                    [class.example-expanded-row]="expandedElement === element"
                    (click)="expandedElement = expandedElement === element ? null : element"></tr>
                  <tr mat-row *matRowDef="let row; columns: ['expandedDetail']" class="example-detail-row"></tr>
                </table>
              </div>
            </mat-card-content>
          </ng-template>

          <mat-paginator
            #paginator
            [length]="totalRows"
            [pageIndex]="currentPage"
            [pageSize]="pageSize"
            (page)="pageChanged($event)"
            [pageSizeOptions]="[5, 10, 20, 30]"
            aria-label="Select page">
          </mat-paginator></div></mat-card-content
    ></mat-card>
  </div>
</div>
