.basic-form {
  margin-left: 30px;
}
table {
  width: 100%;
}

tr.example-detail-row {
  height: 0;
  .mat-column-expandedDetail {
    padding: 0;
  }
}

.example-element-row td {
  border-bottom-width: 0;
}

.example-element-detail {
  overflow: hidden;
  display: flex;
}

.example-element-symbol {
  font-weight: bold;
  font-size: 40px;
  line-height: normal;
}

.example-element-description-attribution {
  opacity: 0.5;
}
/* Spinner container */
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Row and wrap container */
.flex-row-wrap {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

/* Default full width */
.flex-100 {
  flex: 1 1 100%;
}

/* Responsive 100% width for medium screens and up */
.flex-100-md {
  flex: 1 1 100%;
}

/* Comments container */
.comment-container {
  margin-left: 40px;
  padding: 10px;
  margin-top: -28px;
}

/* No comments text */
.no-comments {
  margin-left: 20px;
}

/* Flexbox helper classes */
.d-flex {
  display: flex;
}

.align-items-center {
  align-items: center;
}

.cursor-pointer {
  cursor: pointer;
}

/* Responsive table and card adjustments */
.table-responsive,
.responsive-table {
  width: 100%;
  overflow-x: auto;
}
