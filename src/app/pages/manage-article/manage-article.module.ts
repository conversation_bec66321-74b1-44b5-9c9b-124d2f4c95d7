import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { FeatherModule } from 'angular-feather';
import { allIcons } from 'angular-feather/icons';
import { CKEditorModule } from 'ckeditor4-angular';
import { ArticleComponent } from 'src/app/components/article/article.component';
import { ArticleDialogComponent } from 'src/app/components/article/article-dialog/article-dialog.component';
import { SpinnerModule } from 'src/app/components/spinner/spinner.module';
import { MaterialModule } from 'src/app/material/material.module';

import { ManageArticleComponent } from './manage-article.component';
import { manageArticleRoutes } from './manage-article.routing';

@NgModule({
  declarations: [ManageArticleComponent, ArticleComponent, ArticleDialogComponent],
  imports: [
    RouterModule.forChild(manageArticleRoutes),
    CommonModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    FlexLayoutModule,
    FeatherModule.pick(allIcons),
    NgxDatatableModule,
    SpinnerModule,
    CKEditorModule,
  ],
})
export class ManageArticleModule {}
