import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { FeatherModule } from 'angular-feather';
import { allIcons } from 'angular-feather/icons';
import { NotificationDialogComponent } from 'src/app/components/notification-dialog/notification-dialog.component';
import { SpinnerModule } from 'src/app/components/spinner/spinner.module';
import { MapUrlFromTextDirectiveModule } from 'src/app/directives/map-url-from-text.directive';
import { MaterialModule } from 'src/app/material/material.module';

import { NotificationComponent } from './notification.component';
import { notificationRoutes } from './notification.routing';

@NgModule({
  declarations: [NotificationComponent, NotificationDialogComponent],
  imports: [
    RouterModule.forChild(notificationRoutes),
    CommonModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    FlexLayoutModule,
    FeatherModule.pick(allIcons),
    NgxDatatableModule,
    SpinnerModule,
    MapUrlFromTextDirectiveModule,
  ],
})
export class NotificationModule {}
