<div *ngIf="isPageLoading" fxLayout="row " fxLayoutAlign="center center">
  <app-spinner></app-spinner>
</div>

<div fxLayout="row wrap">
  <div fxFlex.gt-sm="100" fxFlex="100">
    <mat-card>
      <mat-card-content>
        <mat-card-title>Create Notification</mat-card-title>
        <form
          #formDirective="ngForm"
          [formGroup]="notificationForm"
          class="basic-form"
          (ngSubmit)="notificationFormSubmit(formDirective)">
          <div fxLayout="row wrap">
            <div fxFlex.gt-md="25" fxFlex="100">
              <div fxLayout="column">
                <img
                  class="img-notification"
                  [src]="img"
                  onerror="this.src='assets/images/backgrounds/thumbnail-dummy-image.svg'" />
                <button
                  class="clear-image"
                  *ngIf="imageUpload"
                  mat-raised-button
                  aria-label="Clear"
                  (click)="clearUploads()">
                  <mat-icon>highlight_off</mat-icon>Clear
                </button>
              </div>
            </div>
            <div fxFlex.gt-md="75" fxFlex="100">
              <div>
                <mat-form-field>
                  <input
                    matInput
                    formControlName="title"
                    placeholder="Title"
                    name="title"
                    [ngClass]="{
                      'is-invalid': notificationDataSubmitted && notificationFormControl['title'].errors
                    }" />
                </mat-form-field>
                <div
                  *ngIf="notificationDataSubmitted && notificationFormControl['title'].errors"
                  class="invalid-feedback">
                  <div *ngIf="notificationFormControl['title'].errors['required']">Title is required</div>
                </div>
              </div>

              <div>
                <mat-form-field>
                  <input
                    matInput
                    formControlName="content"
                    name="content"
                    placeholder="Message"
                    [ngClass]="{
                      'is-invalid': notificationDataSubmitted && notificationFormControl['content'].errors
                    }" />
                </mat-form-field>
                <div
                  *ngIf="notificationDataSubmitted && notificationFormControl['content'].errors"
                  class="invalid-feedback">
                  <div *ngIf="notificationFormControl['content'].errors['required']">Message is required</div>
                </div>
              </div>
              <div>
                <div fxLayout="columns">
                  <button mat-raised-button color="accent">
                    <label for="images">Select a image to upload </label>
                    <input
                      type="file"
                      id="images"
                      accept="image/png, image/jpeg, image/jpg"
                      required
                      (change)="selectImage($event)" />
                  </button>
                </div>
                <label fxLayout="columns" class="image-informative-cls m-l-0 m-t-10"
                  >Acceptable type: png, jpg, jpeg
                </label>
                <label fxLayout="columns" class="image-informative-cls m-l-0">Upload limit: 15MB </label>
                <label fxLayout="columns" class="image-informative-cls m-l-0"> Width: 200px Height: 200px </label>
              </div>
              <div class="m-t-15">
                <button
                  mat-raised-button
                  [class.spinner]="buttonLoading"
                  [disabled]="!notificationForm.valid || buttonLoading"
                  color="primary">
                  Submit
                </button>
              </div>
            </div>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  </div>
</div>

<div fxLayout="row wrap">
  <div fxFlex="100">
    <mat-card>
      <mat-card-content>
        <div fxLayout="row wrap" fxLayoutAlign="space-between center" fxLayoutGap="40px">
          <div fxFlex.gt-md="10%" fxFlex.gt-md="10%"><mat-card-title class="m-l-10">Notifications</mat-card-title></div>
          <div fxFlex.gt-md="10%" fxFlex.gt-md="20%">
            <mat-form-field>
              <input
                #datepickerInput
                class="mat-date-picker-input"
                matInput
                readonly
                [matDatepicker]="picker"
                [(ngModel)]="notificationDate"
                (ngModelChange)="getDate($event)"
                placeholder="Select a date" />
              <mat-icon class="mat-icon-clear" matDatepickerToggleIcon (click)="clearDate()">clear</mat-icon>
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </div>
        </div>
        <div class="table-responsive">
          <div *ngIf="emptyDataMsg; then notificationEmpty; else tableBlock"></div>
          <ng-template #notificationEmpty>
            <h3 *ngIf="emptyDataMsg" _ngcontent-tqm-c386="" class="mat-h3 text-center ng-star-inserted">
              Notifications Not Found
            </h3></ng-template
          >
          <ng-template #tableBlock
            ><table mat-table [dataSource]="dataSource" class="w-100">
              <ng-container matColumnDef="#">
                <th mat-header-cell *matHeaderCellDef>#</th>
                <td mat-cell *matCellDef="let element; let i = index">
                  {{
                    dataSource.paginator?.pageIndex === 0
                      ? i + 1
                      : 1 + i + (dataSource.paginator?.pageIndex || 0) * (dataSource.paginator?.pageSize || 0)
                  }}
                </td>
              </ng-container>

              <ng-container matColumnDef="title">
                <th mat-header-cell *matHeaderCellDef>Title</th>
                <td mat-cell *matCellDef="let element">
                  <div class="d-flex align-items-center">
                    <img
                      class="img"
                      alt="notification"
                      [src]="element.image"
                      onerror="this.src='assets/images/backgrounds/thumbnail-dummy-image.svg'" />

                    <div class="m-l-15">
                      <p class="lh-md m-t-0">
                        {{ element.title | titlecase }}
                      </p>
                    </div>
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="message">
                <th mat-header-cell *matHeaderCellDef>Message</th>
                <td mat-cell *matCellDef="let element">
                  <div class="d-flex align-items-center">
                    <p class="lh-md m-t-0" [appMapUrlFromText]="element.content">
                      {{ element.content | titlecase }}
                    </p>
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="date">
                <th mat-header-cell *matHeaderCellDef>Date</th>
                <td mat-cell *matCellDef="let element">
                  <div class="d-flex align-items-center">
                    <p class="lh-md m-t-0">
                      {{ element.updatedAt | date }}
                    </p>
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef>Action</th>
                <td mat-cell *matCellDef="let element" class="action-link">
                  <a (click)="openDialog('Delete Notification', element)" class="m-r-10 cursor-pointer" title="Delete">
                    <i-feather name="trash" class="text-danger feather-18"></i-feather>
                  </a>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr></table
          ></ng-template>

          <mat-paginator
            #paginator
            [length]="totalRows"
            [pageIndex]="currentPage"
            [pageSize]="pageSize"
            (page)="pageChanged($event)"
            [pageSizeOptions]="[25, 50, 75, 100]"
            aria-label="Select page">
          </mat-paginator>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
