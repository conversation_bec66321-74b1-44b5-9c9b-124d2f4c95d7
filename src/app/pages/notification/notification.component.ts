import { DatePipe } from '@angular/common';
import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, FormGroupDirective, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatTable, MatTableDataSource } from '@angular/material/table';
import * as moment from 'moment';
import { NotificationDialogComponent } from 'src/app/components/notification-dialog/notification-dialog.component';
import { AdminService } from 'src/app/services/admin.service';
import { NotificationService } from 'src/app/services/notification.service';
import { ProfileService } from 'src/app/services/profile.service';
import { INotification } from 'src/app/types/interfaces/notification.interface';
import { IUserCollection } from 'src/app/types/interfaces/user.interface';

@Component({
  selector: 'app-notification',
  templateUrl: './notification.component.html',
  styleUrls: ['./notification.component.scss'],
})
export class NotificationComponent implements OnInit, AfterViewInit {
  notificationForm: FormGroup = new FormGroup({});
  notificationDataSubmitted: Boolean = false;
  notificationDate: Date | null = null;
  notificationFormData!: INotification;

  emptyDataMsg: Boolean = false;
  buttonLoading: boolean = false;

  userId: string = '';
  adminDetails!: IUserCollection;

  img?: string | ArrayBuffer | null;
  imageUpload?: File | string;

  @ViewChild(MatTable, { static: true }) table: MatTable<any> = Object.create(null);
  displayedColumns: string[] = ['#', 'title', 'message', 'date', 'action'];
  dataSource = new MatTableDataSource();
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator = Object.create(null);

  totalRows = 0;
  pageSize = 25;
  currentPage = 0;
  isPageLoading: boolean = false;

  constructor(
    public datePipe: DatePipe,
    public snackBar: MatSnackBar,
    private adminService: AdminService,
    private notificationService: NotificationService,
    private profileService: ProfileService,
    private fb: FormBuilder,
    public dialog: MatDialog,
  ) {
    this.userId = this.adminService.getAdminId() || '';
  }

  public getDateRange(date?: Date | null): { start: number; end: number } {
    if (date === null) {
      return { start: 0, end: 0 };
    }

    const start = moment(date).startOf('day').valueOf();
    const end = moment(date).endOf('day').valueOf();
    return { start, end };
  }

  ngOnInit(): void {
    const { start, end } = this.getDateRange(this.notificationDate);
    this.getNotifications(this.currentPage, this.pageSize, start, end);

    this.notificationForm = this.fb.group({
      title: ['', Validators.required],
      content: ['', Validators.required],
    });

    this.profileService.getUserById(this.userId).subscribe((res: Object) => {
      if (!res) {
        return;
      }

      this.adminDetails = <IUserCollection>res;
    });
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
  }

  clearDate() {
    this.notificationDate = null;
    this.getNotifications(this.currentPage, this.pageSize, 0, 0);
  }

  getDate(date: Date) {
    const { start, end } = this.getDateRange(date);
    this.currentPage = 0;
    this.getNotifications(this.currentPage, this.pageSize, start, end);
  }

  get notificationFormControl(): { [key: string]: AbstractControl } {
    return this.notificationForm.controls;
  }

  notificationFormSubmit(formDirective: FormGroupDirective) {
    try {
      this.buttonLoading = true;
      this.notificationDataSubmitted = true;
      if (this.notificationForm.invalid) {
        this.buttonLoading = false;
        return;
      }

      this.notificationFormData = this.notificationForm.value;
      const notificationData: INotification = {
        ...this.notificationFormData,
        author: this.adminDetails.name,
      };

      const dataPass = {
        notification: notificationData,
        uploadImage: this.imageUpload,
      };

      this.notificationService.notificationInsert(dataPass.notification, dataPass.uploadImage).subscribe({
        next: (res) => {
          if (!res.status) {
            this.buttonLoading = false;
            this.openSnackBar(res.message, 'Try again');
            return;
          }

          this.buttonLoading = false;
          this.openSnackBar('Notification Created', 'Success');
          const { start, end } = this.getDateRange(this.notificationDate);
          this.getNotifications(this.currentPage, this.pageSize, start, end);
          formDirective.resetForm();
          this.img = '';
          this.imageUpload = '';
        },
        error: (err) => {
          this.buttonLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (error) {
      console.error(error);
      this.buttonLoading = false;
      this.openSnackBar('Something went wrong', 'Try again');
    }
  }

  openSnackBar(message: string, action: string) {
    this.snackBar.open(message, action, {
      duration: 2000,
    });
  }

  getNotifications(page: number, pageSize: number, start: number, end: number) {
    try {
      this.isPageLoading = true;
      this.notificationService.getNotifications(page, pageSize, start, end).subscribe({
        next: (res) => {
          this.isPageLoading = false;
          this.notificationDataSubmitted = false;

          if (!res.status || !res.data) {
            this.emptyDataMsg = true;
            this.isPageLoading = false;
            setTimeout(() => {
              this.paginator.pageIndex = this.currentPage;
              this.paginator.length = 0;
            });
            return;
          }

          this.emptyDataMsg = false;
          this.isPageLoading = false;
          this.dataSource.data = res.data.result;

          setTimeout(() => {
            this.paginator.pageIndex = this.currentPage;
            this.paginator.length = res.data?.total;
          });
        },
        error: (err) => {
          this.isPageLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
        },
      });
    } catch (error) {
      this.isPageLoading = false;
      console.error(error);
    }
  }

  pageChanged(event: PageEvent) {
    try {
      this.pageSize = event.pageSize;
      this.currentPage = event.pageIndex;
      const { start, end } = this.getDateRange(this.notificationDate);
      this.getNotifications(this.currentPage, this.pageSize, start, end);
    } catch (error) {
      console.error(error);
    }
  }

  openDialog(action: string, data: object): void {
    let obj = { ...data, action };
    const dialogRef = this.dialog.open(NotificationDialogComponent, { data: obj });
    dialogRef.afterClosed().subscribe(() => {
      const { start, end } = this.getDateRange(this.notificationDate);
      this.getNotifications(this.currentPage, this.pageSize, start, end);
    });
  }

  selectImage(event: Event): void {
    const element = event.currentTarget as HTMLInputElement;
    let fileList: FileList | null = element.files;

    if (!fileList || fileList.length === 0) {
      return;
    }

    const mimeType = fileList[0].type;
    if (mimeType.match(/image\/*/) == null) {
      return;
    }

    const reader = new FileReader();
    reader.readAsDataURL(fileList[0]);
    reader.onload = (_event) => {
      this.img = reader.result;
    };
    this.imageUpload = <File>fileList[0];
  }

  clearUploads() {
    this.imageUpload = undefined as unknown as File;
    this.img = '';
  }
}
