import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { FeatherModule } from 'angular-feather';
import { allIcons } from 'angular-feather/icons';
import { AdminComponent } from 'src/app/components/admin/admin.component';
import { DialogAdminComponent } from 'src/app/components/admin/dialog-admin/dialog-admin.component';
import { SpinnerModule } from 'src/app/components/spinner/spinner.module';
import { MaterialModule } from 'src/app/material/material.module';

import { ManageAdminComponent } from './manage-admin.component';
import { manageAdminRoutes } from './manage-admin.routing';

@NgModule({
  declarations: [ManageAdminComponent, AdminComponent, DialogAdminComponent],
  imports: [
    RouterModule.forChild(manageAdminRoutes),
    CommonModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    FlexLayoutModule,
    FeatherModule.pick(allIcons),
    NgxDatatableModule,
    SpinnerModule,
  ],
})
export class ManageAdminModule {}
