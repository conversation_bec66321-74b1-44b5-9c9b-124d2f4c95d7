import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { FeatherModule } from 'angular-feather';
import { allIcons } from 'angular-feather/icons';
import { CategoryComponent } from 'src/app/components/channel/category/category.component';
import { DialogComponent } from 'src/app/components/channel/dialog/dialog.component';
import { SpinnerModule } from 'src/app/components/spinner/spinner.module';
import { MaterialModule } from 'src/app/material/material.module';

import { ChannelComponent } from './channel.component';
import { channelRoutes } from './channel.routing';

@NgModule({
  declarations: [ChannelComponent, CategoryComponent, DialogComponent],
  imports: [
    RouterModule.forChild(channelRoutes),
    CommonModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    FlexLayoutModule,
    FeatherModule.pick(allIcons),
    NgxDatatableModule,
    SpinnerModule,
  ],
})
export class ChannelModule {}
