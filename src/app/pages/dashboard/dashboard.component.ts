import { Component, OnInit } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { DashboardService } from 'src/app/services/dashboard.service';

@Component({
  selector: 'app-dash',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class DashboardComponent implements OnInit {
  audiosCount = 0;
  videosCount = 0;
  usersCount = 0;
  verifiedUserCount = 0;
  isLoading = false;
  constructor(private dashboardService: DashboardService, private snackBar: MatSnackBar) {}

  ngOnInit(): void {
    this.getDashboardVideosCount();
  }

  getDashboardVideosCount() {
    try {
      this.isLoading = true;
      this.dashboardService.getDashboardDataCount().subscribe({
        next: (res: any) => {
          if (!res.status || !res.data) {
            this.isLoading = false;

            return;
          }

          this.isLoading = false;
          this.audiosCount = res.data.audios;
          this.videosCount = res.data.videos;
          this.usersCount = res.data.users;
          this.verifiedUserCount = res.data.verifiedUsers;
        },
        error: (err) => {
          this.isLoading = false;
          this.snackBar.open(err.error.message, 'ok', {
            duration: 6000,
          });
          return 'error';
        },
      });
    } catch (err) {
      this.isLoading = false;
      console.error(err);
    }
  }
}
