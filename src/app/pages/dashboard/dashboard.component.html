<div *ngIf="isLoading" fxLayout="row " fxLayoutAlign="center center">
  <app-spinner></app-spinner>
</div>
<div fxLayout="row wrap">
  <div fxFlex="100" fxFlex.gt-md="50%">
    <app-welcome-card></app-welcome-card>
    <div fxLayout="row wrap">
      <div fxFlex="100" fxFlex.gt-xs="50%">
        <app-audio-list-card [count]="audiosCount"></app-audio-list-card>
      </div>
      <div fxFlex="100" fxFlex.gt-xs="50%">
        <app-video-list-card [count]="videosCount"></app-video-list-card>
      </div>
      <div fxFlex="100" fxFlex.gt-xs="50%">
        <app-user-list-card [count]="usersCount"></app-user-list-card>
      </div>
      <div fxFlex="100" fxFlex.gt-xs="50%">
        <app-tbd-list-card [count]="verifiedUserCount"></app-tbd-list-card>
      </div>
    </div>
  </div>
  <div fxFlex="100" fxFlex.gt-md="50%">
    <app-total-storage-space></app-total-storage-space>
  </div>
</div>
<div fxLayout="row wrap">
  <div fxFlex="100" fxFlex.gt-md="100%">
    <app-logged-users-list-card></app-logged-users-list-card>
  </div>
</div>
