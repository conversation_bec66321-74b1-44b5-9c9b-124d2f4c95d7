import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgxDatatableModule } from '@swimlane/ngx-datatable';
import { FeatherModule } from 'angular-feather';
import { allIcons } from 'angular-feather/icons';
import { Ng2SearchPipeModule } from 'ng2-search-filter';
import { DialogComponent } from 'src/app/components/media/dialog/dialog.component';
import { MediaComponent } from 'src/app/components/media/media.component';
import { MediaDialogComponent } from 'src/app/components/media/media-dialog/media-dialog.component';
import { SpinnerModule } from 'src/app/components/spinner/spinner.module';
import { MaterialModule } from 'src/app/material/material.module';

import { MediasComponent } from './medias.component';
import { videoMediaRoutes } from './medias.routing';

@NgModule({
  declarations: [MediasComponent, MediaComponent, DialogComponent, MediaDialogComponent],
  imports: [
    RouterModule.forChild(videoMediaRoutes),
    CommonModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    FlexLayoutModule,
    FeatherModule.pick(allIcons),
    NgxDatatableModule,
    Ng2SearchPipeModule,
    SpinnerModule,
  ],
})
export class MediasModule {}
