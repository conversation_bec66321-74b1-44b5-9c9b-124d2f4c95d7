<div *ngIf="isLoading" fxLayout="row " fxLayoutAlign="center center">
  <app-spinner></app-spinner>
</div>

<div class="login-register main-container">
  <div class="login-register-box error-card text-center">
    <div *ngIf="isSuccess">
      <mat-icon class="text-success">done</mat-icon>
    </div>
    <div *ngIf="isFailed">
      <mat-icon color="warn" class="mat-icon notranslate material-icons mat-icon-no-color">highlight_off</mat-icon>
    </div>
    <h2 class="mat-h2">{{ message }}</h2>
  </div>
</div>
