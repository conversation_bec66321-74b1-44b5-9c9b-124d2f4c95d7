import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { MemberService } from 'src/app/services/member.service';

@Component({
  selector: 'app-verification',
  templateUrl: './verification.component.html',
  styleUrls: ['./verification.component.scss'],
})
export class VerificationComponent implements OnInit {
  token!: string;
  message = '';

  isLoading: boolean = false;
  isSuccess: boolean = false;
  isFailed: boolean = false;

  constructor(private memberService: MemberService, private route: ActivatedRoute) {}

  ngOnInit(): void {
    try {
      this.isLoading = true;
      this.route.params.subscribe((params) => {
        this.token = params['token'] != undefined ? params['token'] : 0;
        this.memberService.emailVerification(this.token).subscribe({
          next: (res) => {
            this.isLoading = false;
            this.isSuccess = true;
            this.message = res.message;
            if (!res.status) {
              this.isFailed = true;
              this.message = res.message;
              this.isSuccess = false;
              this.isLoading = false;
              return;
            }
          },
          error: (err) => {
            this.isLoading = false;
            this.isSuccess = false;
            this.message = err.msg;
            return 'error';
          },
        });
      });
    } catch (err) {
      this.isSuccess = false;
      this.isLoading = false;
      console.error(err);
    }
  }
}
