@use "@angular/material" as mat;
@use 'sass:string';
@import url("https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;600;700&display=swap");

/////////////////////////////////////////////////////
// Theme Fonts
/////////////////////////////////////////////////////
$bodyfont: "DM Sans", sans-serif;
$headingfont: "DM Sans", sans-serif;

/////////////////////////////////////////////////////
// Custom Palettes
/////////////////////////////////////////////////////
// Material 3 Light Theme
$theme: mat.define-theme((
  color: (
    theme-type: light,
    primary: mat.$violet-palette,
    tertiary: mat.$cyan-palette,
  ),
  typography: (
    brand-family: 'DM Sans',
    plain-family: 'DM Sans',
  ),
));

@include mat.core();

:root {
  @include mat.all-component-themes($theme);
}

// Material 3 Dark Theme
$dark-theme: mat.define-theme((
  color: (
    theme-type: dark,
    primary: mat.$violet-palette,
    tertiary: mat.$cyan-palette,
  ),
));

.darkTheme {
  @include mat.all-component-colors($dark-theme);
}
/////////////////////////////////////////////////////
// Custom Variables
/////////////////////////////////////////////////////
$primary: #fb9778;
$accent: #03c9d7;
$warn: #f44336;
$success: #00c292;
$warning: #fec90f;
$danger: #fc4b6c;
$info: #1856e6;
$indigo: #132e6e;
$purple: #6018e6;
$white: #ffffff;
$muted: #777e89;
$light: #949db2;

/* Light colors */
$light-danger: #f9e7eb;
$light-accent: #e5fafb;
$light-success: #ebfaf2;
$light-warning: #fff8ec;
$light-primary: #f1effd;
$light-info: #e3f3fd;
$light-inverse: #f6f6f6;
$light-megna: #e0f2f4;

$sidebarwidth: 265px;
$sidebarIconSize: 18px;
$customizerwidth: 260px;
$minisidebar: 80px;
$box-shadow: 1px 0px 20px rgba(0, 0, 0, 0.08);
$bodylightbg: #fafbfb;
$bodydarkbg: #303030;
$card-radius: 20px;
$card-shadow: $box-shadow;
$borderColor: rgba(0, 0, 0, 0.1);

body {
  font-family: $bodyfont;
}
