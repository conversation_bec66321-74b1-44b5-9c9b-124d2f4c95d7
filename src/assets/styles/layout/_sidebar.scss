@use '../variables' as vars;

.horizontal {
  .page-wrapper {
    margin-left: 0 !important;
  }
}
.leftsidebar {
  width: vars.$sidebarwidth;
  box-shadow: vars.$box-shadow;
  border: 0px;
}

.vsidebar,
.hsidebar {
  padding: 0 15px;

  .routeIcon {
    width: vars.$sidebarIconSize;
    height: vars.$sidebarIconSize;
    margin-right: 10px;
  }
  .menu-list-item {
    margin: 0 0 5px 0;

    .mat-list-item-content {
      border-radius: 6px;
      padding: 0 6px 0 10px;
    }
    &:hover {
      background-color: transparent;
      .mat-list-item-content {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }
  }
}

.minisidebar {
  .leftsidebar {
    width: vars.$minisidebar;
  }
}

.vsidebar .menu-list-item.activeMenu,
.hsidebar .menu-list-item.activeMenu {
  .mat-list-item-content,
  &:hover .mat-list-item-content {
    color: vars.$white;
    background-color: vars.$accent;
  }
}
