/* You can add global styles to this file, and also import other style files */

html,
body {
  height: 100%;
}

body {
  margin: 0;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.cursor-pointer,
.mat-button-wrapper {
  cursor: pointer !important;
}

input[type='file'],
input[type='file']::-webkit-file-upload-button {
  opacity: 0;
  cursor: pointer !important;
  width: 100%;
  position: absolute;
  left: 0;
  height: 100%;
}

.mat-button-wrapper > label,
.mat-button-wrapper > .ng-star-inserted > label {
  cursor: pointer !important;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

.invisible {
  opacity: 0 !important;
}

.spinner:before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  margin-left: -10px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  border-top-color: #fb9778;
  animation: spinner 0.8s linear infinite;
}

.auth-brand {
  position: absolute;
  top: 0;
  img {
    width: 90px;
  }
}

.auth-logo-main {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  img {
    width: 150px;
    height: 150px;
  }
}

.bg-church-landing-page {
  background-color: #2f2e29 !important;
  background-size: cover;
}

.bg-church-landing-page-logo {
  img {
    width: 250px;
    text-align: center;
  }
  h2 {
    color: #fefcff;
  }
}

.upload-image {
  width: 140px;
  height: 140px;
}

.mat-card-content-custom {
  padding: 26px 26px 15px 26px !important;
}

.mat-card-title-custom-cls {
  line-height: 1;
}

.list-card-left-image-custom{
  width: 150px;
  height: 200px;
  object-fit: fill;
}

.list-card-custom{
height: 200px !important;
}

.image-informative-cls{

  margin: 4px 0px 0px 15px;
  font-size: small;
}
